/*
  Warnings:

  - A unique constraint covering the columns `[code]` on the table `MobileOperator` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[pageId]` on the table `Page` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[provider,providerAccountId]` on the table `SocialAccount` will be added. If there are existing duplicate values, this will fail.

*/
-- <PERSON>reateEnum
CREATE TYPE "RefundStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'COMPLETED');

-- AlterEnum
ALTER TYPE "OrderStatus" ADD VALUE 'PAYMENT_FAILED';

-- DropForeignKey
ALTER TABLE "Country" DROP CONSTRAINT "Country_continentId_fkey";

-- DropForeignKey
ALTER TABLE "MobileOperator" DROP CONSTRAINT "MobileOperator_countryId_fkey";

-- DropIndex
DROP INDEX "OdooOrderStatus_uid_idx";

-- DropIndex
DROP INDEX "OdooOrderStatus_variantCode_idx";

-- DropIndex
DROP INDEX "Product_specifications_gin_idx";

-- AlterTable
ALTER TABLE "Banner" ADD COLUMN     "endDate" TIMESTAMP(3),
ADD COLUMN     "language" TEXT NOT NULL DEFAULT 'en',
ADD COLUMN     "startDate" TIMESTAMP(3),
ADD COLUMN     "subtitle" TEXT;

-- AlterTable
ALTER TABLE "HomeFeature" ADD COLUMN     "action" TEXT,
ADD COLUMN     "color" TEXT,
ADD COLUMN     "language" TEXT NOT NULL DEFAULT 'en',
ADD COLUMN     "position" TEXT,
ADD COLUMN     "type" TEXT;

-- AlterTable
ALTER TABLE "MobileOperator" ADD COLUMN     "logoUrl" TEXT,
ADD COLUMN     "nameEn" TEXT,
ADD COLUMN     "nameZh" TEXT;

-- AlterTable
ALTER TABLE "OrderItem" ADD COLUMN     "productId" TEXT;

-- AlterTable
ALTER TABLE "Page" ADD COLUMN     "language" TEXT NOT NULL DEFAULT 'en',
ADD COLUMN     "pageId" TEXT,
ADD COLUMN     "styles" TEXT;

-- AlterTable
ALTER TABLE "PaymentCard" ADD COLUMN     "brand" TEXT,
ADD COLUMN     "last4" TEXT,
ADD COLUMN     "type" TEXT;

-- AlterTable
ALTER TABLE "ProductView" ADD COLUMN     "ipAddress" TEXT,
ADD COLUMN     "userAgent" TEXT;

-- AlterTable
ALTER TABLE "RechargeHistory" ADD COLUMN     "completedAt" TIMESTAMP(3),
ADD COLUMN     "operatorId" TEXT,
ADD COLUMN     "phoneNumber" TEXT;

-- AlterTable
ALTER TABLE "SocialAccount" ADD COLUMN     "lastLoginAt" TIMESTAMP(3),
ADD COLUMN     "picture" TEXT,
ADD COLUMN     "providerAccountId" TEXT;

-- AlterTable
ALTER TABLE "Upload" ADD COLUMN     "category" TEXT,
ADD COLUMN     "path" TEXT,
ADD COLUMN     "uploadedBy" TEXT;

-- CreateTable
CREATE TABLE "Refund" (
    "id" TEXT NOT NULL,
    "orderId" TEXT,
    "paymentId" TEXT,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "reason" TEXT,
    "status" "RefundStatus" NOT NULL DEFAULT 'PENDING',
    "processedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Refund_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Refund_orderId_idx" ON "Refund"("orderId");

-- CreateIndex
CREATE INDEX "Refund_paymentId_idx" ON "Refund"("paymentId");

-- CreateIndex
CREATE INDEX "Refund_status_idx" ON "Refund"("status");

-- CreateIndex
CREATE INDEX "Banner_language_idx" ON "Banner"("language");

-- CreateIndex
CREATE INDEX "Banner_startDate_endDate_idx" ON "Banner"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "HomeFeature_language_idx" ON "HomeFeature"("language");

-- CreateIndex
CREATE UNIQUE INDEX "MobileOperator_code_key" ON "MobileOperator"("code");

-- CreateIndex
CREATE UNIQUE INDEX "Page_pageId_key" ON "Page"("pageId");

-- CreateIndex
CREATE INDEX "Page_pageId_idx" ON "Page"("pageId");

-- CreateIndex
CREATE INDEX "Page_language_idx" ON "Page"("language");

-- CreateIndex
CREATE INDEX "RechargeHistory_operatorId_idx" ON "RechargeHistory"("operatorId");

-- CreateIndex
CREATE UNIQUE INDEX "SocialAccount_provider_providerAccountId_key" ON "SocialAccount"("provider", "providerAccountId");

-- CreateIndex
CREATE INDEX "Upload_category_idx" ON "Upload"("category");

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RechargeHistory" ADD CONSTRAINT "RechargeHistory_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "MobileOperator"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Country" ADD CONSTRAINT "Country_continentId_fkey" FOREIGN KEY ("continentId") REFERENCES "Continent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MobileOperator" ADD CONSTRAINT "MobileOperator_countryId_fkey" FOREIGN KEY ("countryId") REFERENCES "Country"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Refund" ADD CONSTRAINT "Refund_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE SET NULL ON UPDATE CASCADE;
