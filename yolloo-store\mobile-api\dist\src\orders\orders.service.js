"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrdersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const utils_1 = require("../common/utils");
let OrdersService = class OrdersService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createOrder(userId, createOrderDto) {
        const cartItems = await this.prisma.cartItem.findMany({
            where: { userId },
            include: {
                product: true,
                variant: true,
            },
        });
        if (cartItems.length === 0) {
            throw new common_1.BadRequestException('购物车为空，无法创建订单');
        }
        let total = 0;
        let currency = 'USD';
        const orderItems = cartItems.map(item => {
            const price = item.variant ? Number(item.variant.price) : item.product.price;
            const itemCurrency = item.variant ? item.variant.currency : 'USD';
            if (total === 0) {
                currency = itemCurrency;
            }
            total += price * item.quantity;
            return {
                productId: item.productId,
                variantId: item.variantId,
                quantity: item.quantity,
                price: price,
                name: item.product.name,
            };
        });
        const order = await this.prisma.order.create({
            data: {
                userId,
                total,
                status: 'PENDING',
                shippingAddressSnapshot: {
                    name: createOrderDto.shippingAddress.name,
                    phone: createOrderDto.shippingAddress.phone,
                    address1: createOrderDto.shippingAddress.address1,
                    address2: createOrderDto.shippingAddress.address2,
                    city: createOrderDto.shippingAddress.city,
                    state: createOrderDto.shippingAddress.state,
                    postalCode: createOrderDto.shippingAddress.postalCode,
                    country: createOrderDto.shippingAddress.country,
                    billingName: createOrderDto.billingAddress?.name || createOrderDto.shippingAddress.name,
                    billingPhone: createOrderDto.billingAddress?.phone || createOrderDto.shippingAddress.phone,
                    billingAddress1: createOrderDto.billingAddress?.address1 || createOrderDto.shippingAddress.address1,
                    billingAddress2: createOrderDto.billingAddress?.address2 || createOrderDto.shippingAddress.address2,
                    billingCity: createOrderDto.billingAddress?.city || createOrderDto.shippingAddress.city,
                    billingState: createOrderDto.billingAddress?.state || createOrderDto.shippingAddress.state,
                    billingPostalCode: createOrderDto.billingAddress?.postalCode || createOrderDto.shippingAddress.postalCode,
                    billingCountry: createOrderDto.billingAddress?.country || createOrderDto.shippingAddress.country,
                },
                items: {
                    create: orderItems.map(item => ({
                        name: item.name,
                        price: item.price,
                        quantity: item.quantity,
                        productId: item.productId,
                        variantId: item.variantId,
                    })),
                },
            },
        });
        await this.prisma.cartItem.deleteMany({
            where: { userId },
        });
        const paymentIntentId = 'pi_' + Date.now();
        const clientSecret = paymentIntentId + '_secret_' + Math.random().toString(36).substring(2);
        const paymentUrl = 'https://example.com/payment/' + order.id;
        return {
            orderId: order.id,
            paymentIntentId,
            clientSecret,
            total: order.total,
            currency: 'USD',
            paymentUrl,
        };
    }
    async getOrderById(userId, orderId) {
        const order = await this.prisma.order.findFirst({
            where: {
                id: orderId,
                userId,
            },
            include: {
                items: true,
            },
        });
        if (!order) {
            throw new common_1.NotFoundException('Order not found');
        }
        const formattedItems = order.items.map(item => ({
            id: item.id,
            productCode: item.productCode || 'Unknown SKU',
            name: item.variantText || 'Unknown Product',
            price: item.price,
            quantity: item.quantity,
            imageUrl: 'https://example.com/default-product.jpg',
        }));
        const formattedEsims = [];
        return {
            id: order.id,
            status: order.status,
            paymentStatus: 'PENDING',
            total: order.total,
            currency: 'USD',
            items: formattedItems,
            esims: formattedEsims,
            createdAt: order.createdAt.toISOString(),
            updatedAt: order.updatedAt.toISOString(),
        };
    }
    async getUserOrders(userId, query) {
        const { status, page = 1, pageSize = 20 } = query;
        const skip = (page - 1) * pageSize;
        const where = { userId };
        if (status) {
            where.status = status;
        }
        const [orders, total] = await Promise.all([
            this.prisma.order.findMany({
                where,
                skip,
                take: pageSize,
                orderBy: {
                    createdAt: 'desc',
                },
                include: {
                    items: {
                        select: {
                            id: true,
                            productCode: true,
                            variantText: true,
                            quantity: true,
                            price: true,
                        },
                    },
                },
            }),
            this.prisma.order.count({ where }),
        ]);
        const formattedOrders = orders.map(order => ({
            id: order.id,
            status: order.status,
            total: order.total,
            currency: 'USD',
            items: order.items.map(item => ({
                id: item.id,
                productCode: item.productCode || 'Unknown SKU',
                productName: item.variantText || 'Unknown Product',
                quantity: item.quantity,
                price: item.price,
            })),
            createdAt: utils_1.DateFormatter.iso(order.createdAt),
        }));
        return {
            orders: formattedOrders,
            pagination: {
                total,
                page,
                pageSize,
                hasMore: skip + orders.length < total,
            },
        };
    }
};
OrdersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OrdersService);
exports.OrdersService = OrdersService;
//# sourceMappingURL=orders.service.js.map