"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CardsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const app_constants_1 = require("../common/constants/app.constants");
const utils_1 = require("../common/utils");
let CardsService = CardsService_1 = class CardsService {
    prisma;
    logger = new common_1.Logger(CardsService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getUserCards(userId) {
        try {
            this.logger.log(`Fetching cards for user: ${userId}`);
            const cards = await this.prisma.yollooCard.findMany({
                where: { userId },
                include: {
                    esims: true,
                },
            });
            if (cards.length === 0) {
                this.logger.warn(`No cards found for user ${userId}`);
                return { cards: [] };
            }
            this.logger.log(`Found ${cards.length} cards for user ${userId}`);
            const formattedCards = cards.map(card => ({
                id: card.id,
                number: card.number,
                status: card.status,
                type: card.type,
                customName: card.customName,
                activationDate: card.activationDate?.toISOString(),
                expiryDate: card.expiryDate?.toISOString(),
                esimCount: card.esims.length,
            }));
            return { cards: formattedCards };
        }
        catch (error) {
            this.logger.error(`Error fetching cards for user ${userId}:`, error);
            return { cards: [] };
        }
    }
    async getCardById(userId, cardId) {
        try {
            this.logger.log(`Fetching card ${cardId} for user ${userId}`);
            const card = await this.prisma.yollooCard.findFirst({
                where: {
                    id: cardId,
                    userId,
                },
                include: {
                    esims: {
                        include: {
                            product: true,
                        },
                    },
                },
            });
            if (!card) {
                this.logger.warn(`Card ${cardId} not found for user ${userId}`);
                throw new common_1.NotFoundException('Card not found');
            }
            this.logger.log(`Found card ${cardId} with ${card.esims.length} eSIMs`);
            const formattedEsims = card.esims.map(esim => ({
                id: esim.id,
                iccid: esim.iccid,
                status: esim.status,
                activationDate: esim.activationDate ? utils_1.DateFormatter.iso(esim.activationDate) : null,
                expiryDate: esim.expiryDate ? utils_1.DateFormatter.iso(esim.expiryDate) : null,
                product: esim.product
                    ? {
                        id: esim.product.id,
                        name: esim.product.name,
                        description: esim.product.description,
                    }
                    : null,
            }));
            return {
                id: card.id,
                number: card.number,
                status: card.status,
                type: card.type,
                customName: card.customName,
                activationDate: card.activationDate ? utils_1.DateFormatter.iso(card.activationDate) : null,
                expiryDate: card.expiryDate ? utils_1.DateFormatter.iso(card.expiryDate) : null,
                esims: formattedEsims,
            };
        }
        catch (error) {
            this.logger.error(`Error fetching card ${cardId} for user ${userId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new Error('Failed to fetch card details');
        }
    }
    async registerCard(userId, registerCardDto) {
        try {
            this.logger.log(`Registering card ${registerCardDto.cardNumber} for user ${userId}`);
            const existingCard = await this.prisma.yollooCard.findUnique({
                where: { number: registerCardDto.cardNumber },
            });
            if (existingCard) {
                this.logger.warn(`Card number ${registerCardDto.cardNumber} already registered`);
                throw new common_1.BadRequestException('此卡号已被注册');
            }
            const card = await this.prisma.yollooCard.create({
                data: {
                    userId,
                    number: registerCardDto.cardNumber,
                    status: app_constants_1.CARD_STATUS.INACTIVE,
                    type: 'Standard',
                    customName: registerCardDto.customName,
                },
            });
            this.logger.log(`Card ${card.number} registered successfully for user ${userId}`);
            return {
                id: card.id,
                number: card.number,
                status: card.status,
                message: '卡片注册成功，请激活后使用',
            };
        }
        catch (error) {
            this.logger.error(`Error registering card for user ${userId}:`, error);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new Error('Failed to register card');
        }
    }
    async activateCard(userId, cardId) {
        const card = await this.prisma.yollooCard.findFirst({
            where: {
                id: cardId,
                userId,
            },
        });
        if (!card) {
            throw new common_1.NotFoundException('卡片不存在');
        }
        if (card.status === app_constants_1.CARD_STATUS.ACTIVE) {
            throw new common_1.BadRequestException('卡片已激活');
        }
        if (card.status === app_constants_1.CARD_STATUS.EXPIRED) {
            throw new common_1.BadRequestException('卡片已过期，无法激活');
        }
        const activationDate = new Date();
        const expiryDate = utils_1.DateUtils.addDays(activationDate, 365);
        const updatedCard = await this.prisma.yollooCard.update({
            where: { id: cardId },
            data: {
                status: app_constants_1.CARD_STATUS.ACTIVE,
                activationDate,
                expiryDate,
            },
        });
        return {
            id: updatedCard.id,
            number: updatedCard.number,
            status: updatedCard.status,
            activationDate: updatedCard.activationDate ? utils_1.DateFormatter.iso(updatedCard.activationDate) : null,
            expiryDate: updatedCard.expiryDate ? utils_1.DateFormatter.iso(updatedCard.expiryDate) : null,
            message: '卡片激活成功',
        };
    }
    async addEsimToCard(userId, cardId, addEsimDto) {
        const card = await this.prisma.yollooCard.findFirst({
            where: {
                id: cardId,
                userId,
            },
        });
        if (!card) {
            throw new common_1.NotFoundException('Card not found');
        }
        const product = await this.prisma.product.findUnique({
            where: { id: addEsimDto.productId },
        });
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        const existingEsim = await this.prisma.esim.findUnique({
            where: { iccid: addEsimDto.iccid },
        });
        if (existingEsim) {
            throw new common_1.BadRequestException('此ICCID已被使用');
        }
        const esim = await this.prisma.esim.create({
            data: {
                iccid: addEsimDto.iccid,
                status: 'Inactive',
                yollooCardId: cardId,
                productId: addEsimDto.productId,
            },
        });
        return {
            id: esim.id,
            iccid: esim.iccid,
            status: esim.status,
            message: 'eSIM添加成功',
        };
    }
    async activateEsim(userId, esimId) {
        const esim = await this.prisma.esim.findFirst({
            where: {
                id: esimId,
                yollooCard: {
                    userId,
                },
            },
        });
        if (!esim) {
            throw new common_1.NotFoundException('eSIM not found');
        }
        if (esim.status === app_constants_1.CARD_STATUS.ACTIVE) {
            throw new common_1.BadRequestException('eSIM已激活');
        }
        const activationDate = new Date();
        const expiryDate = utils_1.DateUtils.addDays(activationDate, 30);
        const updatedEsim = await this.prisma.esim.update({
            where: { id: esimId },
            data: {
                status: app_constants_1.CARD_STATUS.ACTIVE,
                activationDate,
                expiryDate,
            },
        });
        return {
            id: updatedEsim.id,
            status: updatedEsim.status,
            activationDate: updatedEsim.activationDate ? utils_1.DateFormatter.iso(updatedEsim.activationDate) : null,
            expiryDate: updatedEsim.expiryDate ? utils_1.DateFormatter.iso(updatedEsim.expiryDate) : null,
            qrCodeUrl: 'https://example.com/qr-code.png',
            message: 'eSIM激活成功',
        };
    }
};
CardsService = CardsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CardsService);
exports.CardsService = CardsService;
//# sourceMappingURL=cards.service.js.map