import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma.service';
export interface PaymentIntent {
    id: string;
    clientSecret: string;
    amount: number;
    currency: string;
    status: 'requires_payment_method' | 'requires_confirmation' | 'requires_action' | 'processing' | 'succeeded' | 'canceled';
}
export interface PaymentMethod {
    id: string;
    type: 'card' | 'alipay' | 'wechat_pay' | 'bank_transfer';
    card?: {
        brand: string;
        last4: string;
        exp_month: number;
        exp_year: number;
    };
}
export declare class PaymentService {
    private prisma;
    private configService;
    private readonly logger;
    private readonly stripeEnabled;
    private readonly alipayEnabled;
    private readonly wechatPayEnabled;
    constructor(prisma: PrismaService, configService: ConfigService);
    createPaymentIntent(amount: number, currency: string, paymentMethodTypes?: string[], metadata?: Record<string, string>): Promise<PaymentIntent>;
    private createStripePaymentIntent;
    private createAlipayPaymentIntent;
    private createWechatPaymentIntent;
    private createMockPaymentIntent;
    confirmPaymentIntent(paymentIntentId: string): Promise<PaymentIntent>;
    getPaymentMethods(customerId?: string): Promise<PaymentMethod[]>;
    processRefund(paymentIntentId: string, amount?: number, reason?: string): Promise<{
        id: string;
        status: string;
        amount: number;
    }>;
    getAvailablePaymentMethods(): Promise<string[]>;
    validateWebhook(payload: string, signature: string): Promise<boolean>;
    handleWebhook(payload: any): Promise<void>;
    private handlePaymentSucceeded;
    private handlePaymentFailed;
    createCustomer(email: string, name?: string): Promise<string>;
}
