import { PrismaService } from '../prisma.service';
import { NumberRetentionQueryDto } from './dto/number-retention-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class NumberRetentionService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getNumberRetentionPackages(query: NumberRetentionQueryDto, ctx: RequestContext): Promise<{
        packages: {
            id: string;
            name: string;
            description: string;
            price: number;
            originalPrice: number;
            currency: string;
            planType: string;
            features: string[];
            validity: string;
            category: string;
            isPopular: boolean;
            imageUrl: string;
        }[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
        categories: {
            id: string;
            name: string;
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    private getFallbackNumberRetentionPackages;
    getPackageById(packageId: string, ctx: RequestContext): Promise<{
        id: string;
        name: string;
        description: string;
        price: number;
        originalPrice: number;
        currency: string;
        planType: string;
        features: string[];
        validity: string;
        category: string;
        isPopular: boolean;
        imageUrl: string;
        detailedFeatures: any;
        terms: string[];
    }>;
}
