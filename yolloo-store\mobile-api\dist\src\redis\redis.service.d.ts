import { OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
export declare class RedisService implements OnModuleDestroy {
    private configService;
    private redisClient;
    private isConnected;
    private isBuildTime;
    constructor(configService: ConfigService);
    getClient(): Redis | null;
    isRedisConnected(): boolean;
    get(key: string): Promise<string | null>;
    set(key: string, value: string, expireSeconds?: number): Promise<boolean>;
    del(key: string): Promise<boolean>;
    onModuleDestroy(): Promise<void>;
}
