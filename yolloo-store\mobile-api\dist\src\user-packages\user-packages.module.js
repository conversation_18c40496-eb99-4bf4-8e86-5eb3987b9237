"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPackagesModule = void 0;
const common_1 = require("@nestjs/common");
const user_packages_controller_1 = require("./user-packages.controller");
const user_packages_service_1 = require("./user-packages.service");
const prisma_service_1 = require("../prisma.service");
const auth_module_1 = require("../auth/auth.module");
let UserPackagesModule = class UserPackagesModule {
};
UserPackagesModule = __decorate([
    (0, common_1.Module)({
        imports: [auth_module_1.AuthModule],
        controllers: [user_packages_controller_1.UserPackagesController],
        providers: [user_packages_service_1.UserPackagesService, prisma_service_1.PrismaService],
        exports: [user_packages_service_1.UserPackagesService],
    })
], UserPackagesModule);
exports.UserPackagesModule = UserPackagesModule;
//# sourceMappingURL=user-packages.module.js.map