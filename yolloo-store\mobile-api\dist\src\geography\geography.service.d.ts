import { PrismaService } from '../prisma.service';
import { RatingService } from '../rating/rating.service';
import { RequestContext } from '../common/interfaces/context.interface';
import { ProductsByCountryDto } from './dto/geography-query.dto';
export declare class GeographyService {
    private readonly prisma;
    private readonly ratingService;
    private readonly logger;
    constructor(prisma: PrismaService, ratingService: RatingService);
    getContinents(ctx: RequestContext): Promise<{
        continents: {
            id: string;
            name: any;
            nameEn: any;
            nameZh: any;
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getCountriesByContinent(continent: string, ctx: RequestContext): Promise<{
        continent: {
            id: string;
            name: any;
        };
        countries: any;
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getProductsByCountry(dto: ProductsByCountryDto, ctx: RequestContext): Promise<{
        products: {
            id: string;
            name: string;
            description: string;
            price: number;
            currency: string;
            imageUrl: string;
            dataSize: number;
            planType: string;
            duration: number;
            countries: string[];
            countryCode: string | undefined;
            rating: number;
            reviewCount: number;
            ratingDistribution: {
                1: number;
                2: number;
                3: number;
                4: number;
                5: number;
            };
            category: {
                id: string;
                name: string;
            };
            variants: {
                id: string;
                price: number;
                currency: string;
            }[];
        }[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
            totalPages: number;
        };
        filters: {
            countryCode: string | undefined;
            planType: "Total" | "Daily" | undefined;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error?: undefined;
    } | {
        products: any[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
            totalPages: number;
        };
        filters: {
            countryCode: string | undefined;
            planType: "Total" | "Daily" | undefined;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error: string;
    }>;
    private generateMockProducts;
    private generateAllCountriesProducts;
    getProductFilters(ctx: RequestContext): Promise<{
        planTypes: {
            value: string;
            label: string;
            description: string;
        }[];
        dataSizes: {
            value: string;
            label: string;
        }[];
        priceRange: {
            min: number;
            max: number;
        };
        sortOptions: {
            value: string;
            label: string;
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error?: undefined;
    } | {
        planTypes: {
            value: string;
            label: string;
            description: string;
        }[];
        dataSizes: {
            value: string;
            label: string;
        }[];
        priceRange: {
            min: number;
            max: number;
        };
        sortOptions: {
            value: string;
            label: string;
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error: string;
    }>;
}
