#!/bin/bash

# 设置错误时退出并打印执行的命令
set -ex

echo "🚀 Starting deployment process..."

# 1. 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# 2. 检查 Docker Compose 是否安装
if ! command -v docker compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# 3. 停止并删除旧容器（如果存在）
echo "🔄 Stopping old containers..."
docker compose down || true

# 4. 拉取最新代码（如果在 CI/CD 环境中，这步可能不需要）
# git pull origin main

# 5. 构建新镜像
echo "🏗️ Building new Docker images..."
docker compose build --no-cache

# 6. 启动新容器
echo "🌟 Starting new containers..."
# 检查网络是否存在，如果不存在则创建
if ! docker network inspect my-network &> /dev/null; then
    echo "🌐 Creating my-network..."
    docker network create my-network
fi

docker compose up -d

# 等待服务启动
echo "⏳ Waiting for services to start..."
sleep 15

# 7. 清理未使用的镜像
echo "🧹 Cleaning up old images..."
docker image prune -f

# 8. 检查服务状态和迁移
echo "🔍 Checking service status..."
if docker compose ps | grep -q "Up"; then
    echo "✅ Containers are running!"

    # 检查主应用健康状态
    echo "🏥 Checking main app health..."
    for i in {1..12}; do
        if curl -f http://localhost:8000/api/health >/dev/null 2>&1; then
            echo "✅ Main app is healthy!"
            break
        else
            echo "⏳ Waiting for main app to be ready... ($i/12)"
            sleep 10
        fi
        if [ $i -eq 12 ]; then
            echo "❌ Main app health check failed!"
            docker compose logs app
            exit 1
        fi
    done

    # 检查 mobile-api 健康状态
    echo "📱 Checking mobile-api health..."
    for i in {1..6}; do
        if curl -f http://localhost:4000/api/mobile/health >/dev/null 2>&1; then
            echo "✅ Mobile-API is healthy!"
            break
        else
            echo "⏳ Waiting for mobile-api to be ready... ($i/6)"
            sleep 10
        fi
        if [ $i -eq 6 ]; then
            echo "❌ Mobile-API health check failed!"
            docker compose logs mobile-api
            exit 1
        fi
    done

    echo "✅ Deployment completed successfully!"
    echo "💡 Main app: http://localhost:8000"
    echo "💡 Mobile API: http://localhost:4000"
    echo "💡 Check logs: docker compose logs -f app"
    echo "💡 Check mobile-api logs: docker compose logs -f mobile-api"
else
    echo "❌ Deployment failed. Showing logs..."
    docker compose logs
    exit 1
fi