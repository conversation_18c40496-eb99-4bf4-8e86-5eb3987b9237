import { ArticlesService } from './articles.service';
import { ArticleQueryDto, ArticleCategoryQueryDto } from './dto/article-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class ArticlesController {
    private readonly articlesService;
    constructor(articlesService: ArticlesService);
    getCategories(query: ArticleCategoryQueryDto, ctx: RequestContext): Promise<{
        categories: {
            id: string;
            name: string;
            description: string;
            icon: string;
            articleCount: number;
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getArticles(query: ArticleQueryDto, ctx: RequestContext): Promise<{
        articles: never[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
            totalPages?: undefined;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        filters?: undefined;
    } | {
        articles: {
            id: any;
            title: any;
            summary: any;
            category: any;
            imageUrl: any;
            content: any;
            author: {
                id: string;
                name: string;
                avatar: string;
            };
            publishDate: any;
            readCount: number;
            likeCount: number;
            tags: string[];
        }[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
            totalPages: number;
            hasMore?: undefined;
        };
        filters: {
            category: string | undefined;
            country: string | undefined;
            tag: string | undefined;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getArticleById(id: string, ctx: RequestContext): Promise<{
        article: {
            id: string;
            title: string;
            summary: string | null;
            category: string;
            imageUrl: string;
            content: string;
            fullContent: string;
            author: {
                id: string;
                name: string;
                avatar: string;
            };
            publishDate: string;
            readCount: number;
            likeCount: number;
            tags: string[];
        };
        relatedArticles: {
            id: string;
            title: string;
            summary: string;
            category: string;
            imageUrl: string;
            content: string;
            author: {
                id: string;
                name: string;
                avatar: string;
            };
            publishDate: string;
            readCount: number;
            likeCount: number;
            tags: string[];
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error?: undefined;
        articleId?: undefined;
    } | {
        error: string;
        articleId: string;
        article?: undefined;
        relatedArticles?: undefined;
        context?: undefined;
    } | {
        article: {
            fullContent: any;
            comments: any;
            metadata: {
                wordCount: any;
                readingTime: number;
                lastUpdated: any;
            };
            id: any;
            title: any;
            summary: any;
            category: any;
            imageUrl: any;
            content: any;
            author: {
                id: string;
                name: string;
                avatar: string;
            };
            publishDate: any;
            readCount: number;
            likeCount: number;
            tags: string[];
        };
        relatedArticles: {
            id: any;
            title: any;
            summary: any;
            category: any;
            imageUrl: any;
            content: any;
            author: {
                id: string;
                name: string;
                avatar: string;
            };
            publishDate: any;
            readCount: number;
            likeCount: number;
            tags: string[];
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error?: undefined;
        articleId?: undefined;
    }>;
}
