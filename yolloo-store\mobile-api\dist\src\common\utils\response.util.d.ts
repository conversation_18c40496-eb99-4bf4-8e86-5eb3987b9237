import { RequestContext } from '../interfaces/context.interface';
export interface PaginationResponse {
    total: number;
    page: number;
    pageSize: number;
    hasMore: boolean;
}
export interface ApiResponse<T> {
    data?: T;
    pagination?: PaginationResponse;
    context?: {
        language: string;
        theme: string;
        currency: string;
    };
    message?: string;
}
export declare class ResponseUtil {
    static createPaginationResponse(total: number, page?: number, pageSize?: number): PaginationResponse;
    static createContextResponse(ctx: RequestContext): {
        language: string;
        theme: string;
        currency: string;
    };
    static createListResponse<T>(items: T[], total: number, page: number, pageSize: number, ctx: RequestContext, additionalData?: any): any;
    static createSuccessResponse<T>(data: T, message?: string, ctx?: RequestContext): ApiResponse<T>;
    private static getItemsKey;
}
