"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardsController = void 0;
const common_1 = require("@nestjs/common");
const cards_service_1 = require("./cards.service");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const register_card_dto_1 = require("./dto/register-card.dto");
const add_esim_dto_1 = require("./dto/add-esim.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let CardsController = class CardsController {
    cardsService;
    constructor(cardsService) {
        this.cardsService = cardsService;
    }
    getUserCards(user) {
        if (!user) {
            throw new common_1.BadRequestException('User not authenticated');
        }
        return this.cardsService.getUserCards(user.id);
    }
    getCardById(user, cardId) {
        if (!user) {
            throw new common_1.BadRequestException('User not authenticated');
        }
        if (!cardId || cardId.trim() === '') {
            throw new common_1.BadRequestException('Card ID is required');
        }
        return this.cardsService.getCardById(user.id, cardId);
    }
    registerCard(user, registerCardDto) {
        if (!user) {
            throw new common_1.BadRequestException('User not authenticated');
        }
        return this.cardsService.registerCard(user.id, registerCardDto);
    }
    activateCard(user, cardId) {
        if (!user) {
            throw new common_1.BadRequestException('User not authenticated');
        }
        if (!cardId || cardId.trim() === '') {
            throw new common_1.BadRequestException('Card ID is required');
        }
        return this.cardsService.activateCard(user.id, cardId);
    }
    addEsimToCard(user, cardId, addEsimDto) {
        if (!user) {
            throw new common_1.BadRequestException('User not authenticated');
        }
        if (!cardId || cardId.trim() === '') {
            throw new common_1.BadRequestException('Card ID is required');
        }
        return this.cardsService.addEsimToCard(user.id, cardId, addEsimDto);
    }
};
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], CardsController.prototype, "getUserCards", null);
__decorate([
    (0, common_1.Get)(':cardId'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('cardId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], CardsController.prototype, "getCardById", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, register_card_dto_1.RegisterCardDto]),
    __metadata("design:returntype", void 0)
], CardsController.prototype, "registerCard", null);
__decorate([
    (0, common_1.Post)(':cardId/activate'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('cardId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], CardsController.prototype, "activateCard", null);
__decorate([
    (0, common_1.Post)(':cardId/esims'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('cardId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, add_esim_dto_1.AddEsimDto]),
    __metadata("design:returntype", void 0)
], CardsController.prototype, "addEsimToCard", null);
CardsController = __decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cards'),
    __metadata("design:paramtypes", [cards_service_1.CardsService])
], CardsController);
exports.CardsController = CardsController;
//# sourceMappingURL=cards.controller.js.map