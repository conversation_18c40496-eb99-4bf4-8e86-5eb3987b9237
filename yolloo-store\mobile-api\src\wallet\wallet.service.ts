import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma.service';

@Injectable()
export class WalletService {
  private readonly logger = new Logger(WalletService.name);

  constructor(private prisma: PrismaService) {}

  async getOrCreateWallet(userId: string) {
    let wallet = await this.prisma.wallet.findUnique({
      where: { userId },
      include: {
        transactions: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        paymentCards: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (!wallet) {
      wallet = await this.prisma.wallet.create({
        data: {
          userId,
          balance: 0,
          currency: 'USD',
        },
        include: {
          transactions: true,
          paymentCards: true,
        },
      });
    }

    return wallet;
  }

  async addTransaction(
    walletId: string,
    type: 'DEPOSIT' | 'PAYMENT' | 'REFUND' | 'WITHDRAWAL',
    amount: number,
    currency: string,
    description: string,
    reference?: string,
    metadata?: any
  ) {
    return await this.prisma.transaction.create({
      data: {
        walletId,
        type,
        amount,
        currency,
        status: 'PENDING',
        description,
        reference,
        metadata,
      },
    });
  }

  async updateTransactionStatus(
    transactionId: string,
    status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  ) {
    const transaction = await this.prisma.transaction.update({
      where: { id: transactionId },
      data: { status },
      include: { wallet: true },
    });

    // 如果交易完成，更新钱包余额
    if (status === 'COMPLETED') {
      const balanceChange = transaction.type === 'DEPOSIT' || transaction.type === 'REFUND'
        ? transaction.amount
        : -transaction.amount;

      await this.prisma.wallet.update({
        where: { id: transaction.walletId },
        data: {
          balance: {
            increment: balanceChange,
          },
        },
      });
    }

    return transaction;
  }

  async processPayment(userId: string, amount: number, description: string, orderId?: string) {
    const wallet = await this.getOrCreateWallet(userId);

    if (wallet.balance < amount) {
      throw new BadRequestException('Insufficient wallet balance');
    }

    // 创建支付交易
    const transaction = await this.addTransaction(
      wallet.id,
      'PAYMENT',
      amount,
      wallet.currency,
      description,
      orderId,
      { orderId }
    );

    // 立即完成交易并更新余额
    await this.updateTransactionStatus(transaction.id, 'COMPLETED');

    return transaction;
  }

  async addPaymentCard(
    userId: string,
    cardData: {
      type: string;
      brand: string;
      last4: string;
      expiryMonth: number;
      expiryYear: number;
      isDefault?: boolean;
    }
  ) {
    const wallet = await this.getOrCreateWallet(userId);

    // 如果设置为默认卡，先取消其他卡的默认状态
    if (cardData.isDefault) {
      await this.prisma.paymentCard.updateMany({
        where: { walletId: wallet.id },
        data: { isDefault: false },
      });
    }

    return await this.prisma.paymentCard.create({
      data: {
        walletId: wallet.id,
        cardNumber: `****-****-****-${cardData.last4}`, // 模拟加密存储
        cardHolder: 'Card Holder', // 默认值
        cardType: cardData.brand.toUpperCase(),
        type: cardData.type,
        brand: cardData.brand,
        last4: cardData.last4,
        expiryMonth: cardData.expiryMonth,
        expiryYear: cardData.expiryYear,
        isDefault: cardData.isDefault || false,
      },
    });
  }

  async removePaymentCard(userId: string, cardId: string) {
    const wallet = await this.getOrCreateWallet(userId);

    const result = await this.prisma.paymentCard.deleteMany({
      where: {
        id: cardId,
        walletId: wallet.id,
      },
    });

    if (result.count === 0) {
      throw new NotFoundException('Payment card not found');
    }

    return { success: true, message: 'Payment card removed' };
  }

  async setDefaultPaymentCard(userId: string, cardId: string) {
    const wallet = await this.getOrCreateWallet(userId);

    // 验证卡片存在
    const card = await this.prisma.paymentCard.findFirst({
      where: {
        id: cardId,
        walletId: wallet.id,
      },
    });

    if (!card) {
      throw new NotFoundException('Payment card not found');
    }

    // 取消其他卡的默认状态
    await this.prisma.paymentCard.updateMany({
      where: { walletId: wallet.id },
      data: { isDefault: false },
    });

    // 设置新的默认卡
    await this.prisma.paymentCard.update({
      where: { id: cardId },
      data: { isDefault: true },
    });

    return { success: true, message: 'Default payment card updated' };
  }
}
