"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UploadService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const fs = require("fs");
const path = require("path");
const crypto = require("crypto");
let UploadService = UploadService_1 = class UploadService {
    prisma;
    logger = new common_1.Logger(UploadService_1.name);
    uploadDir = path.join(process.cwd(), 'uploads');
    allowedMimeTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp',
        'image/gif'
    ];
    maxFileSize = 5 * 1024 * 1024;
    constructor(prisma) {
        this.prisma = prisma;
        this.ensureUploadDirectory();
    }
    ensureUploadDirectory() {
        if (!fs.existsSync(this.uploadDir)) {
            fs.mkdirSync(this.uploadDir, { recursive: true });
            this.logger.log(`Created upload directory: ${this.uploadDir}`);
        }
        const subDirs = ['products', 'users', 'categories', 'rewards', 'temp'];
        subDirs.forEach(dir => {
            const dirPath = path.join(this.uploadDir, dir);
            if (!fs.existsSync(dirPath)) {
                fs.mkdirSync(dirPath, { recursive: true });
            }
        });
    }
    async uploadImage(file, category = 'temp', userId) {
        this.validateFile(file);
        const fileExtension = path.extname(file.originalname);
        const fileName = `${crypto.randomUUID()}${fileExtension}`;
        const relativePath = `${category}/${fileName}`;
        const fullPath = path.join(this.uploadDir, relativePath);
        try {
            fs.writeFileSync(fullPath, file.buffer);
            const uploadRecord = await this.prisma.upload.create({
                data: {
                    userId: userId || 'anonymous',
                    filename: fileName,
                    originalName: file.originalname,
                    mimeType: file.mimetype,
                    size: file.size,
                    path: relativePath,
                    category: category,
                    uploadedBy: userId,
                    url: `/uploads/${relativePath}`,
                },
            });
            this.logger.log(`File uploaded successfully: ${fileName}`);
            return {
                id: uploadRecord.id,
                fileName: fileName,
                originalName: file.originalname,
                url: uploadRecord.url,
                size: file.size,
                mimeType: file.mimetype,
                category: category,
                uploadedAt: uploadRecord.createdAt,
            };
        }
        catch (error) {
            this.logger.error(`Failed to upload file: ${error.message}`);
            if (fs.existsSync(fullPath)) {
                fs.unlinkSync(fullPath);
            }
            throw new common_1.BadRequestException('Failed to upload file');
        }
    }
    async uploadMultipleImages(files, category = 'temp', userId) {
        const results = [];
        const errors = [];
        for (const file of files) {
            try {
                const result = await this.uploadImage(file, category, userId);
                results.push(result);
            }
            catch (error) {
                errors.push({
                    fileName: file.originalname,
                    error: error.message,
                });
            }
        }
        return {
            success: results,
            errors: errors,
            total: files.length,
            uploaded: results.length,
            failed: errors.length,
        };
    }
    async getUploadById(uploadId) {
        const upload = await this.prisma.upload.findUnique({
            where: { id: uploadId },
        });
        if (!upload) {
            throw new common_1.BadRequestException('Upload not found');
        }
        return upload;
    }
    async deleteUpload(uploadId, userId) {
        const upload = await this.prisma.upload.findUnique({
            where: { id: uploadId },
        });
        if (!upload) {
            throw new common_1.BadRequestException('Upload not found');
        }
        if (userId && upload.uploadedBy !== userId) {
            throw new common_1.BadRequestException('Unauthorized to delete this upload');
        }
        try {
            const fullPath = path.join(this.uploadDir, upload.path || upload.filename);
            if (fs.existsSync(fullPath)) {
                fs.unlinkSync(fullPath);
            }
            await this.prisma.upload.delete({
                where: { id: uploadId },
            });
            this.logger.log(`Upload deleted successfully: ${uploadId}`);
            return { success: true, message: 'Upload deleted successfully' };
        }
        catch (error) {
            this.logger.error(`Failed to delete upload: ${error.message}`);
            throw new common_1.BadRequestException('Failed to delete upload');
        }
    }
    async getUserUploads(userId, category) {
        const where = { uploadedBy: userId };
        if (category) {
            where.category = category;
        }
        const uploads = await this.prisma.upload.findMany({
            where,
            orderBy: { createdAt: 'desc' },
        });
        return uploads;
    }
    async cleanupTempFiles(olderThanHours = 24) {
        const cutoffDate = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
        const tempUploads = await this.prisma.upload.findMany({
            where: {
                category: 'temp',
                createdAt: { lt: cutoffDate },
            },
        });
        let deletedCount = 0;
        for (const upload of tempUploads) {
            try {
                await this.deleteUpload(upload.id);
                deletedCount++;
            }
            catch (error) {
                this.logger.warn(`Failed to cleanup temp file ${upload.id}: ${error.message}`);
            }
        }
        this.logger.log(`Cleaned up ${deletedCount} temporary files`);
        return { deletedCount };
    }
    validateFile(file) {
        if (!file) {
            throw new common_1.BadRequestException('No file provided');
        }
        if (!this.allowedMimeTypes.includes(file.mimetype)) {
            throw new common_1.BadRequestException(`Invalid file type. Allowed types: ${this.allowedMimeTypes.join(', ')}`);
        }
        if (file.size > this.maxFileSize) {
            throw new common_1.BadRequestException(`File too large. Maximum size: ${this.maxFileSize / 1024 / 1024}MB`);
        }
    }
    getImageUrl(uploadId) {
        return `/api/uploads/${uploadId}`;
    }
    async moveUploadToCategory(uploadId, newCategory) {
        const upload = await this.getUploadById(uploadId);
        const oldPath = path.join(this.uploadDir, upload.path || upload.filename);
        const newFileName = path.basename(upload.path || upload.filename);
        const newRelativePath = `${newCategory}/${newFileName}`;
        const newPath = path.join(this.uploadDir, newRelativePath);
        try {
            fs.renameSync(oldPath, newPath);
            const updatedUpload = await this.prisma.upload.update({
                where: { id: uploadId },
                data: {
                    category: newCategory,
                    path: newRelativePath,
                    url: `/uploads/${newRelativePath}`,
                },
            });
            return updatedUpload;
        }
        catch (error) {
            this.logger.error(`Failed to move upload: ${error.message}`);
            throw new common_1.BadRequestException('Failed to move upload');
        }
    }
};
UploadService = UploadService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UploadService);
exports.UploadService = UploadService;
//# sourceMappingURL=upload.service.js.map