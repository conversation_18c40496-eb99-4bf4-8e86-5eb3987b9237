"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NumberRetentionController = void 0;
const common_1 = require("@nestjs/common");
const number_retention_service_1 = require("./number-retention.service");
const number_retention_query_dto_1 = require("./dto/number-retention-query.dto");
const request_context_decorator_1 = require("../common/decorators/request-context.decorator");
const public_decorator_1 = require("../common/decorators/public.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let NumberRetentionController = class NumberRetentionController {
    numberRetentionService;
    constructor(numberRetentionService) {
        this.numberRetentionService = numberRetentionService;
    }
    getNumberRetentionPackages(query, ctx) {
        return this.numberRetentionService.getNumberRetentionPackages(query, ctx);
    }
    getPackageById(packageId, ctx) {
        return this.numberRetentionService.getPackageById(packageId, ctx);
    }
};
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [number_retention_query_dto_1.NumberRetentionQueryDto, Object]),
    __metadata("design:returntype", void 0)
], NumberRetentionController.prototype, "getNumberRetentionPackages", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(':packageId'),
    __param(0, (0, common_1.Param)('packageId')),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], NumberRetentionController.prototype, "getPackageById", null);
NumberRetentionController = __decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('number-retention'),
    __metadata("design:paramtypes", [number_retention_service_1.NumberRetentionService])
], NumberRetentionController);
exports.NumberRetentionController = NumberRetentionController;
//# sourceMappingURL=number-retention.controller.js.map