"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GeographyService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeographyService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const rating_service_1 = require("../rating/rating.service");
const app_constants_1 = require("../common/constants/app.constants");
let GeographyService = GeographyService_1 = class GeographyService {
    prisma;
    ratingService;
    logger = new common_1.Logger(GeographyService_1.name);
    constructor(prisma, ratingService) {
        this.prisma = prisma;
        this.ratingService = ratingService;
    }
    async getContinents(ctx) {
        const isZh = ctx.language.startsWith('zh');
        try {
            const continents = await this.prisma.continent.findMany({
                where: {
                    isActive: true,
                },
                orderBy: {
                    code: 'asc',
                },
            });
            if (continents.length > 0) {
                const formattedContinents = continents.map(continent => ({
                    id: continent.code,
                    name: isZh ? continent.nameZh : continent.nameEn,
                    nameEn: continent.nameEn,
                    nameZh: continent.nameZh,
                }));
                return {
                    continents: formattedContinents,
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            const fallbackContinents = Object.keys(app_constants_1.CONTINENT_NAMES).map(continentKey => ({
                id: continentKey,
                name: isZh ? app_constants_1.CONTINENT_NAMES[continentKey].zh : app_constants_1.CONTINENT_NAMES[continentKey].en,
                nameEn: app_constants_1.CONTINENT_NAMES[continentKey].en,
                nameZh: app_constants_1.CONTINENT_NAMES[continentKey].zh,
            }));
            return {
                continents: fallbackContinents,
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching continents:', error);
            const fallbackContinents = Object.keys(app_constants_1.CONTINENT_NAMES).map(continentKey => ({
                id: continentKey,
                name: isZh ? app_constants_1.CONTINENT_NAMES[continentKey].zh : app_constants_1.CONTINENT_NAMES[continentKey].en,
                nameEn: app_constants_1.CONTINENT_NAMES[continentKey].en,
                nameZh: app_constants_1.CONTINENT_NAMES[continentKey].zh,
            }));
            return {
                continents: fallbackContinents,
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
    }
    async getCountriesByContinent(continent, ctx) {
        const isZh = ctx.language.startsWith('zh');
        try {
            const continentRecord = await this.prisma.continent.findFirst({
                where: {
                    code: continent,
                    isActive: true,
                },
                include: {
                    countries: {
                        where: {
                            isActive: true,
                        },
                        orderBy: {
                            nameEn: 'asc',
                        },
                    },
                },
            });
            if (continentRecord && continentRecord.countries.length > 0) {
                const formattedCountries = continentRecord.countries.map(country => ({
                    code: country.code,
                    name: isZh ? country.nameZh : country.nameEn,
                    nameEn: country.nameEn,
                    nameZh: country.nameZh,
                    flagUrl: country.flagUrl,
                    currency: country.currency,
                    timezone: country.timezone,
                }));
                return {
                    continent: {
                        id: continentRecord.code,
                        name: isZh ? continentRecord.nameZh : continentRecord.nameEn,
                    },
                    countries: formattedCountries,
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            const countries = app_constants_1.CONTINENT_COUNTRIES[continent] || [];
            const formattedCountries = countries.map(country => ({
                code: country.code,
                name: isZh ? country.name : country.nameEn,
                nameEn: country.nameEn,
                nameZh: country.name,
            }));
            return {
                continent: {
                    id: continent,
                    name: isZh ? app_constants_1.CONTINENT_NAMES[continent]?.zh : app_constants_1.CONTINENT_NAMES[continent]?.en,
                },
                countries: formattedCountries,
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching countries by continent:', error);
            const countries = app_constants_1.CONTINENT_COUNTRIES[continent] || [];
            const formattedCountries = countries.map(country => ({
                code: country.code,
                name: isZh ? country.name : country.nameEn,
                nameEn: country.nameEn,
                nameZh: country.name,
            }));
            return {
                continent: {
                    id: continent,
                    name: isZh ? app_constants_1.CONTINENT_NAMES[continent]?.zh : app_constants_1.CONTINENT_NAMES[continent]?.en,
                },
                countries: formattedCountries,
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
    }
    async getProductsByCountry(dto, ctx) {
        const { countryCode, planType, page = 1, pageSize = 10 } = dto;
        const isZh = ctx.language.startsWith('zh');
        try {
            const whereConditions = {
                status: 'ACTIVE',
                off_shelve: false,
            };
            if (countryCode) {
                whereConditions.OR = [
                    { countryCode: { contains: countryCode, mode: 'insensitive' } },
                    { country: { contains: countryCode, mode: 'insensitive' } },
                ];
            }
            if (planType) {
                whereConditions.planType = { equals: planType, mode: 'insensitive' };
            }
            const total = await this.prisma.product.count({
                where: whereConditions,
            });
            const skip = (page - 1) * pageSize;
            const products = await this.prisma.product.findMany({
                where: whereConditions,
                include: {
                    category: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    variants: {
                        select: {
                            id: true,
                            price: true,
                            currency: true,
                        },
                        orderBy: {
                            price: 'asc',
                        },
                    },
                    reviews: {
                        select: {
                            rating: true,
                        },
                    },
                },
                skip,
                take: pageSize,
                orderBy: [
                    { createdAt: 'desc' },
                    { price: 'asc' },
                ],
            });
            const formattedProducts = await Promise.all(products.map(async (product) => {
                const ratingData = await this.ratingService.calculateProductRating(product.id);
                const lowestPrice = product.variants.length > 0
                    ? Math.min(...product.variants.map(v => Number(v.price)))
                    : Number(product.price);
                const currency = product.variants.length > 0
                    ? product.variants[0].currency || ctx.currency
                    : ctx.currency;
                let countries = [];
                try {
                    const specs = typeof product.specifications === 'string'
                        ? JSON.parse(product.specifications)
                        : product.specifications;
                    countries = specs?.countries || [];
                }
                catch (error) {
                    this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
                }
                if (countries.length === 0 && product.country) {
                    countries = product.country.split(/[,;]/).map(c => c.trim()).filter(c => c);
                }
                return {
                    id: product.id,
                    name: product.name,
                    description: product.description,
                    price: lowestPrice,
                    currency: currency,
                    imageUrl: product.images && product.images.length > 0
                        ? product.images[0]
                        : '/images/defaults/product-placeholder.jpg',
                    dataSize: product.dataSize || 0,
                    planType: product.planType || 'Total',
                    duration: 30,
                    countries: countries,
                    countryCode: product.countryCode || countryCode,
                    rating: ratingData.averageRating,
                    reviewCount: ratingData.totalReviews,
                    ratingDistribution: ratingData.ratingDistribution,
                    category: {
                        id: product.category.id,
                        name: product.category.name,
                    },
                    variants: product.variants.map(variant => ({
                        id: variant.id,
                        price: Number(variant.price),
                        currency: variant.currency,
                    })),
                };
            }));
            return {
                products: formattedProducts,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                },
                filters: {
                    countryCode,
                    planType,
                },
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching products by country:', error);
            const mockProducts = this.generateMockProducts(countryCode || '', planType || '', isZh, ctx);
            const skip = (page - 1) * pageSize;
            const paginatedProducts = mockProducts.slice(skip, skip + pageSize);
            return {
                products: paginatedProducts,
                pagination: {
                    page,
                    pageSize,
                    total: mockProducts.length,
                    totalPages: Math.ceil(mockProducts.length / pageSize),
                },
                filters: {
                    countryCode,
                    planType,
                },
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
                error: 'Database query failed, showing fallback data',
            };
        }
    }
    generateMockProducts(countryCode, planType, isZh, ctx) {
        const products = [];
        if (countryCode === 'JP') {
            if (planType === 'Total' || !planType) {
                products.push({
                    id: 'jp-total-500mb',
                    name: isZh ? '500MB 流量卡' : '500MB Data Card',
                    description: isZh ? '流量 | 全日本' : 'Data | All Japan',
                    price: 39.9,
                    currency: ctx.currency,
                    imageUrl: '/images/products/jp-500mb.jpg',
                    dataSize: 500,
                    planType: 'Total',
                    duration: 30,
                    countries: ['JP'],
                    countryCode: 'JP',
                    rating: 4.8,
                    reviewCount: 1250,
                }, {
                    id: 'jp-total-1gb',
                    name: isZh ? '1GB 流量卡' : '1GB Data Card',
                    description: isZh ? '流量 | 全日本' : 'Data | All Japan',
                    price: 39.9,
                    currency: ctx.currency,
                    imageUrl: '/images/products/jp-1gb.jpg',
                    dataSize: 1024,
                    planType: 'Total',
                    duration: 30,
                    countries: ['JP'],
                    countryCode: 'JP',
                    rating: 4.7,
                    reviewCount: 980,
                }, {
                    id: 'jp-total-3gb',
                    name: isZh ? '3GB 流量卡' : '3GB Data Card',
                    description: isZh ? '流量 | 全日本' : 'Data | All Japan',
                    price: 39.9,
                    currency: ctx.currency,
                    imageUrl: '/images/products/jp-3gb.jpg',
                    dataSize: 3072,
                    planType: 'Total',
                    duration: 30,
                    countries: ['JP'],
                    countryCode: 'JP',
                    rating: 4.9,
                    reviewCount: 1580,
                }, {
                    id: 'jp-total-10gb',
                    name: isZh ? '10GB 流量卡' : '10GB Data Card',
                    description: isZh ? '流量 | 全日本' : 'Data | All Japan',
                    price: 39.9,
                    currency: ctx.currency,
                    imageUrl: '/images/products/jp-10gb.jpg',
                    dataSize: 10240,
                    planType: 'Total',
                    duration: 30,
                    countries: ['JP'],
                    countryCode: 'JP',
                    rating: 4.6,
                    reviewCount: 750,
                });
            }
            if (planType === 'Daily' || !planType) {
                products.push({
                    id: 'jp-daily-1gb',
                    name: isZh ? '1GB 天卡' : '1GB Daily Card',
                    description: isZh ? '流量 | 全日本' : 'Data | All Japan',
                    price: 15.9,
                    currency: ctx.currency,
                    imageUrl: '/images/products/jp-daily-1gb.jpg',
                    dataSize: 1024,
                    planType: 'Daily',
                    duration: 1,
                    countries: ['JP'],
                    countryCode: 'JP',
                    rating: 4.5,
                    reviewCount: 420,
                });
            }
        }
        if (countryCode === 'KR') {
            if (planType === 'Total' || !planType) {
                products.push({
                    id: 'kr-total-1gb',
                    name: isZh ? '1GB 流量卡' : '1GB Data Card',
                    description: isZh ? '流量 | 全韩国' : 'Data | All Korea',
                    price: 29.9,
                    currency: ctx.currency,
                    imageUrl: '/images/products/kr-1gb.jpg',
                    dataSize: 1024,
                    planType: 'Total',
                    duration: 30,
                    countries: ['KR'],
                    countryCode: 'KR',
                    rating: 4.7,
                    reviewCount: 680,
                });
            }
        }
        if (!countryCode) {
            return this.generateAllCountriesProducts(planType, isZh, ctx);
        }
        return products;
    }
    generateAllCountriesProducts(planType, isZh, ctx) {
        const allProducts = [];
        Object.keys(app_constants_1.CONTINENT_COUNTRIES).forEach(continent => {
            const countries = app_constants_1.CONTINENT_COUNTRIES[continent];
            countries.slice(0, 3).forEach(country => {
                const products = this.generateMockProducts(country.code, planType, isZh, ctx);
                allProducts.push(...products);
            });
        });
        return allProducts;
    }
    async getProductFilters(ctx) {
        const isZh = ctx.language.startsWith('zh');
        try {
            const [planTypes, priceRange, dataSizeOptions] = await Promise.all([
                this.prisma.product.findMany({
                    where: {
                        status: 'ACTIVE',
                        off_shelve: false,
                        planType: { not: null },
                    },
                    select: { planType: true },
                    distinct: ['planType'],
                }),
                this.prisma.product.aggregate({
                    where: {
                        status: 'ACTIVE',
                        off_shelve: false,
                    },
                    _min: { price: true },
                    _max: { price: true },
                }),
                this.prisma.product.findMany({
                    where: {
                        status: 'ACTIVE',
                        off_shelve: false,
                        dataSize: { not: null },
                    },
                    select: { dataSize: true },
                    distinct: ['dataSize'],
                    orderBy: { dataSize: 'asc' },
                }),
            ]);
            const formattedPlanTypes = planTypes
                .filter(p => p.planType)
                .map(p => ({
                value: p.planType,
                label: isZh
                    ? (p.planType === 'Total' ? '总量卡' : p.planType === 'Daily' ? '天卡' : p.planType)
                    : (p.planType === 'Total' ? 'Total Data' : p.planType === 'Daily' ? 'Daily Data' : p.planType),
                description: isZh
                    ? (p.planType === 'Total' ? '一次性流量包，用完为止' : p.planType === 'Daily' ? '每日固定流量，按天计费' : '')
                    : (p.planType === 'Total' ? 'One-time data package' : p.planType === 'Daily' ? 'Fixed daily data allowance' : ''),
            }));
            const formattedDataSizes = dataSizeOptions
                .filter(d => d.dataSize && d.dataSize > 0)
                .map(d => {
                const sizeInMB = d.dataSize;
                let label;
                if (sizeInMB >= 1024) {
                    const sizeInGB = sizeInMB / 1024;
                    label = sizeInGB % 1 === 0 ? `${sizeInGB}GB` : `${sizeInGB.toFixed(1)}GB`;
                }
                else {
                    label = `${sizeInMB}MB`;
                }
                return {
                    value: sizeInMB.toString(),
                    label: label,
                };
            });
            return {
                planTypes: formattedPlanTypes.length > 0 ? formattedPlanTypes : [
                    {
                        value: 'Total',
                        label: isZh ? '总量卡' : 'Total Data',
                        description: isZh ? '一次性流量包，用完为止' : 'One-time data package'
                    },
                    {
                        value: 'Daily',
                        label: isZh ? '天卡' : 'Daily Data',
                        description: isZh ? '每日固定流量，按天计费' : 'Fixed daily data allowance'
                    },
                ],
                dataSizes: formattedDataSizes.length > 0 ? formattedDataSizes : [
                    { value: '500', label: '500MB' },
                    { value: '1024', label: '1GB' },
                    { value: '3072', label: '3GB' },
                    { value: '10240', label: '10GB' },
                ],
                priceRange: {
                    min: priceRange._min.price || 0,
                    max: priceRange._max.price || 100,
                },
                sortOptions: [
                    { value: 'price', label: isZh ? '价格' : 'Price' },
                    { value: 'dataSize', label: isZh ? '流量大小' : 'Data Size' },
                    { value: 'rating', label: isZh ? '评分' : 'Rating' },
                    { value: 'createdAt', label: isZh ? '最新' : 'Latest' },
                ],
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching product filters:', error);
            return {
                planTypes: [
                    {
                        value: 'Total',
                        label: isZh ? '总量卡' : 'Total Data',
                        description: isZh ? '一次性流量包，用完为止' : 'One-time data package'
                    },
                    {
                        value: 'Daily',
                        label: isZh ? '天卡' : 'Daily Data',
                        description: isZh ? '每日固定流量，按天计费' : 'Fixed daily data allowance'
                    },
                ],
                dataSizes: [
                    { value: '500', label: '500MB' },
                    { value: '1024', label: '1GB' },
                    { value: '3072', label: '3GB' },
                    { value: '10240', label: '10GB' },
                ],
                priceRange: {
                    min: 0,
                    max: 100,
                },
                sortOptions: [
                    { value: 'price', label: isZh ? '价格' : 'Price' },
                    { value: 'dataSize', label: isZh ? '流量大小' : 'Data Size' },
                    { value: 'rating', label: isZh ? '评分' : 'Rating' },
                ],
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
                error: 'Database query failed, showing default filters',
            };
        }
    }
};
GeographyService = GeographyService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        rating_service_1.RatingService])
], GeographyService);
exports.GeographyService = GeographyService;
//# sourceMappingURL=geography.service.js.map