#!/bin/bash

echo "🔍 Checking migration status across environments..."

# 检查主应用迁移状态
echo ""
echo "📊 Main Application Migration Status:"
echo "======================================"
if docker compose exec app npx prisma migrate status 2>/dev/null; then
    echo "✅ Main app migration status checked successfully"
else
    echo "❌ Failed to check main app migration status"
    echo "💡 Make sure the main app container is running"
fi

# 检查 mobile-api 迁移状态
echo ""
echo "📱 Mobile-API Migration Status:"
echo "==============================="
if docker compose exec mobile-api npx prisma migrate status 2>/dev/null; then
    echo "✅ Mobile-API migration status checked successfully"
else
    echo "❌ Failed to check mobile-api migration status"
    echo "💡 Make sure the mobile-api container is running"
fi

# 检查数据库连接
echo ""
echo "🗄️ Database Connection Test:"
echo "============================="
if docker compose exec app npx prisma db execute --stdin <<< "SELECT 1;" >/dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
fi

echo ""
echo "📋 Summary:"
echo "==========="
echo "💡 If you see migration issues, run: docker compose exec app npx prisma migrate deploy"
echo "💡 To view detailed logs: docker compose logs app"
echo "💡 To view mobile-api logs: docker compose logs mobile-api"
