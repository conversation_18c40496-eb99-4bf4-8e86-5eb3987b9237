# 使用与主应用相同的数据库连接
# 现在统一使用 DATABASE_URL，因为迁移由主应用管理
# 本地开发环境 - 与主应用使用相同数据库
DATABASE_URL="postgresql://postgres:123456789@localhost:5432/yolloo_store?sslmode=disable"
# 生产环境
#DATABASE_URL="postgres://admin:<EMAIL>:5432/yolloo_store?sslmode=disable"

# 使用不同的JWT密钥名称
MOBILE_API_JWT_SECRET="Qk216ITtrMa2J/2JwDVoWnTSuIeykWf9rFsrGjOQp3Q="
PORT=4000
MOBILE_API_PORT=4000

# Google Maps API Key (需要替换为实际的API密钥)
# 注意：如果不设置有效的API密钥，反向地理编码功能将返回默认值
# 获取API密钥: https://developers.google.com/maps/documentation/geocoding/get-api-key
GOOGLE_MAPS_API_KEY="YOUR_GOOGLE_MAPS_API_KEY"
