import { Response } from 'express';
export declare class StaticController {
    private readonly staticDir;
    constructor();
    private ensureStaticDirectories;
    private createDefaultPlaceholders;
    private generatePlaceholderSVG;
    serveDefaultImage(filename: string, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    serveProductImage(filename: string, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    serveCategoryImage(filename: string, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    serveRewardImage(filename: string, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    private serveStaticFile;
}
