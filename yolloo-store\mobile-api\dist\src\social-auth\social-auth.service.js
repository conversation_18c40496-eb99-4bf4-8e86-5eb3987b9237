"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SocialAuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocialAuthService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("@nestjs/axios");
const prisma_service_1 = require("../prisma.service");
const rxjs_1 = require("rxjs");
let SocialAuthService = SocialAuthService_1 = class SocialAuthService {
    prisma;
    configService;
    httpService;
    logger = new common_1.Logger(SocialAuthService_1.name);
    constructor(prisma, configService, httpService) {
        this.prisma = prisma;
        this.configService = configService;
        this.httpService = httpService;
    }
    async verifyGoogleToken(token) {
        try {
            const googleClientId = this.configService.get('GOOGLE_CLIENT_ID');
            if (!googleClientId) {
                throw new common_1.BadRequestException('Google authentication not configured');
            }
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`https://oauth2.googleapis.com/tokeninfo?id_token=${token}`));
            const payload = response.data;
            if (payload.aud !== googleClientId) {
                throw new common_1.BadRequestException('Invalid Google token audience');
            }
            if (payload.exp < Date.now() / 1000) {
                throw new common_1.BadRequestException('Google token expired');
            }
            return {
                id: payload.sub,
                email: payload.email,
                name: payload.name,
                picture: payload.picture,
                provider: 'google',
            };
        }
        catch (error) {
            this.logger.error('Google token verification failed:', error);
            throw new common_1.BadRequestException('Invalid Google token');
        }
    }
    async verifyFacebookToken(token) {
        try {
            const facebookAppId = this.configService.get('FACEBOOK_APP_ID');
            const facebookAppSecret = this.configService.get('FACEBOOK_APP_SECRET');
            if (!facebookAppId || !facebookAppSecret) {
                throw new common_1.BadRequestException('Facebook authentication not configured');
            }
            const verifyResponse = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`https://graph.facebook.com/debug_token?input_token=${token}&access_token=${facebookAppId}|${facebookAppSecret}`));
            const tokenData = verifyResponse.data.data;
            if (!tokenData.is_valid) {
                throw new common_1.BadRequestException('Invalid Facebook token');
            }
            const userResponse = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`https://graph.facebook.com/me?fields=id,name,email,picture&access_token=${token}`));
            const userData = userResponse.data;
            return {
                id: userData.id,
                email: userData.email,
                name: userData.name,
                picture: userData.picture?.data?.url,
                provider: 'facebook',
            };
        }
        catch (error) {
            this.logger.error('Facebook token verification failed:', error);
            throw new common_1.BadRequestException('Invalid Facebook token');
        }
    }
    async verifyAppleToken(token) {
        try {
            const appleTeamId = this.configService.get('APPLE_TEAM_ID');
            const appleClientId = this.configService.get('APPLE_CLIENT_ID');
            if (!appleTeamId || !appleClientId) {
                throw new common_1.BadRequestException('Apple authentication not configured');
            }
            const parts = token.split('.');
            if (parts.length !== 3) {
                throw new common_1.BadRequestException('Invalid Apple token format');
            }
            const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
            if (payload.aud !== appleClientId) {
                throw new common_1.BadRequestException('Invalid Apple token audience');
            }
            if (payload.exp < Date.now() / 1000) {
                throw new common_1.BadRequestException('Apple token expired');
            }
            return {
                id: payload.sub,
                email: payload.email,
                name: payload.name || 'Apple User',
                provider: 'apple',
            };
        }
        catch (error) {
            this.logger.error('Apple token verification failed:', error);
            throw new common_1.BadRequestException('Invalid Apple token');
        }
    }
    async verifyWechatCode(code) {
        try {
            const wechatAppId = this.configService.get('WECHAT_APP_ID');
            const wechatAppSecret = this.configService.get('WECHAT_APP_SECRET');
            if (!wechatAppId || !wechatAppSecret) {
                throw new common_1.BadRequestException('WeChat authentication not configured');
            }
            const tokenResponse = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`https://api.weixin.qq.com/sns/oauth2/access_token?appid=${wechatAppId}&secret=${wechatAppSecret}&code=${code}&grant_type=authorization_code`));
            const tokenData = tokenResponse.data;
            if (tokenData.errcode) {
                throw new common_1.BadRequestException(`WeChat API error: ${tokenData.errmsg}`);
            }
            const userResponse = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`https://api.weixin.qq.com/sns/userinfo?access_token=${tokenData.access_token}&openid=${tokenData.openid}`));
            const userData = userResponse.data;
            if (userData.errcode) {
                throw new common_1.BadRequestException(`WeChat API error: ${userData.errmsg}`);
            }
            return {
                id: userData.openid,
                email: `${userData.openid}@wechat.local`,
                name: userData.nickname,
                picture: userData.headimgurl,
                provider: 'wechat',
            };
        }
        catch (error) {
            this.logger.error('WeChat code verification failed:', error);
            throw new common_1.BadRequestException('Invalid WeChat code');
        }
    }
    async findOrCreateSocialUser(socialInfo) {
        try {
            let socialAccount = await this.prisma.socialAccount.findUnique({
                where: {
                    provider_providerAccountId: {
                        provider: socialInfo.provider,
                        providerAccountId: socialInfo.id,
                    },
                },
                include: {
                    user: true,
                },
            });
            if (socialAccount) {
                await this.prisma.socialAccount.update({
                    where: { id: socialAccount.id },
                    data: {
                        name: socialInfo.name,
                        email: socialInfo.email,
                        picture: socialInfo.picture,
                        lastLoginAt: new Date(),
                    },
                });
                return socialAccount.user;
            }
            let user = await this.prisma.user.findUnique({
                where: { email: socialInfo.email },
            });
            if (!user) {
                user = await this.prisma.user.create({
                    data: {
                        email: socialInfo.email,
                        name: socialInfo.name,
                        image: socialInfo.picture,
                        emailVerified: new Date(),
                    },
                });
            }
            await this.prisma.socialAccount.create({
                data: {
                    userId: user.id,
                    provider: socialInfo.provider,
                    providerId: socialInfo.id,
                    providerAccountId: socialInfo.id,
                    name: socialInfo.name,
                    email: socialInfo.email,
                    picture: socialInfo.picture,
                    lastLoginAt: new Date(),
                },
            });
            return user;
        }
        catch (error) {
            this.logger.error('Failed to find or create social user:', error);
            throw new common_1.BadRequestException('Failed to process social login');
        }
    }
    async unlinkSocialAccount(userId, provider) {
        try {
            const result = await this.prisma.socialAccount.deleteMany({
                where: {
                    userId,
                    provider,
                },
            });
            if (result.count === 0) {
                throw new common_1.BadRequestException('Social account not found');
            }
        }
        catch (error) {
            this.logger.error('Failed to unlink social account:', error);
            throw new common_1.BadRequestException('Failed to unlink social account');
        }
    }
    async getUserSocialAccounts(userId) {
        try {
            const socialAccounts = await this.prisma.socialAccount.findMany({
                where: { userId },
                select: {
                    provider: true,
                    name: true,
                    email: true,
                    picture: true,
                    lastLoginAt: true,
                },
            });
            return socialAccounts;
        }
        catch (error) {
            this.logger.error('Failed to get user social accounts:', error);
            return [];
        }
    }
};
SocialAuthService = SocialAuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        config_1.ConfigService,
        axios_1.HttpService])
], SocialAuthService);
exports.SocialAuthService = SocialAuthService;
//# sourceMappingURL=social-auth.service.js.map