"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PagesController = void 0;
const common_1 = require("@nestjs/common");
const pages_service_1 = require("./pages.service");
const page_config_query_dto_1 = require("./dto/page-config-query.dto");
const page_content_query_dto_1 = require("./dto/page-content-query.dto");
const request_context_decorator_1 = require("../common/decorators/request-context.decorator");
const public_decorator_1 = require("../common/decorators/public.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let PagesController = class PagesController {
    pagesService;
    constructor(pagesService) {
        this.pagesService = pagesService;
    }
    getPageConfigs(query, ctx) {
        return this.pagesService.getPageConfigs(query, ctx);
    }
    getPageContent(query, ctx) {
        return this.pagesService.getPageContent(query, ctx);
    }
};
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('configs'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [page_config_query_dto_1.PageConfigQueryDto, Object]),
    __metadata("design:returntype", void 0)
], PagesController.prototype, "getPageConfigs", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('content'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [page_content_query_dto_1.PageContentQueryDto, Object]),
    __metadata("design:returntype", void 0)
], PagesController.prototype, "getPageContent", null);
PagesController = __decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('pages'),
    __metadata("design:paramtypes", [pages_service_1.PagesService])
], PagesController);
exports.PagesController = PagesController;
//# sourceMappingURL=pages.controller.js.map