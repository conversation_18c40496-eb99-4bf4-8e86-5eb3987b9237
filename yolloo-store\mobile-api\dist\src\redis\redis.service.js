"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const ioredis_1 = require("ioredis");
let RedisService = class RedisService {
    configService;
    redisClient = null;
    isConnected = false;
    isBuildTime = process.env.NODE_ENV === 'production' && !process.env.IS_RUNTIME;
    constructor(configService) {
        this.configService = configService;
        if (this.isBuildTime) {
            console.log('Build time detected, Redis connection deferred until runtime');
            return;
        }
        const redisUrl = this.configService.get('REDIS_URL');
        if (!redisUrl) {
            console.warn('REDIS_URL not found in environment variables. Using default connection.');
        }
        try {
            this.redisClient = new ioredis_1.default(redisUrl || 'redis://localhost:6379');
            this.redisClient.on('error', (err) => {
                console.error('Redis connection error:', err);
                this.isConnected = false;
            });
            this.redisClient.on('connect', () => {
                console.log('Successfully connected to Redis');
                this.isConnected = true;
            });
        }
        catch (error) {
            console.error('Failed to initialize Redis client:', error);
            this.redisClient = null;
        }
    }
    getClient() {
        return this.redisClient;
    }
    isRedisConnected() {
        return this.isConnected;
    }
    async get(key) {
        if (!this.redisClient || this.isBuildTime) {
            return null;
        }
        try {
            return await this.redisClient.get(key);
        }
        catch (error) {
            console.error(`Error getting key ${key} from Redis:`, error);
            return null;
        }
    }
    async set(key, value, expireSeconds) {
        if (!this.redisClient || this.isBuildTime) {
            return false;
        }
        try {
            if (expireSeconds) {
                await this.redisClient.setex(key, expireSeconds, value);
            }
            else {
                await this.redisClient.set(key, value);
            }
            return true;
        }
        catch (error) {
            console.error(`Error setting key ${key} in Redis:`, error);
            return false;
        }
    }
    async del(key) {
        if (!this.redisClient || this.isBuildTime) {
            return false;
        }
        try {
            await this.redisClient.del(key);
            return true;
        }
        catch (error) {
            console.error(`Error deleting key ${key} from Redis:`, error);
            return false;
        }
    }
    async onModuleDestroy() {
        if (this.redisClient) {
            await this.redisClient.quit();
        }
    }
};
RedisService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], RedisService);
exports.RedisService = RedisService;
//# sourceMappingURL=redis.service.js.map