import { PrismaService } from '../prisma.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
export declare class OrdersService {
    private prisma;
    constructor(prisma: PrismaService);
    createOrder(userId: string, createOrderDto: CreateOrderDto): Promise<{
        orderId: string;
        paymentIntentId: string;
        clientSecret: string;
        total: number;
        currency: string;
        paymentUrl: string;
    }>;
    getOrderById(userId: string, orderId: string): Promise<{
        id: string;
        status: import(".prisma/client").OrderStatus;
        paymentStatus: string;
        total: number;
        currency: string;
        items: {
            id: string;
            productCode: string;
            name: string;
            price: number;
            quantity: number;
            imageUrl: string;
        }[];
        esims: never[];
        createdAt: string;
        updatedAt: string;
    }>;
    getUserOrders(userId: string, query: OrderQueryDto): Promise<{
        orders: {
            id: string;
            status: import(".prisma/client").OrderStatus;
            total: number;
            currency: string;
            items: {
                id: string;
                productCode: string;
                productName: string;
                quantity: number;
                price: number;
            }[];
            createdAt: string;
        }[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
    }>;
}
