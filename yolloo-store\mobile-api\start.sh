#!/bin/sh

echo "=== Mobile API Startup Script ==="

# 加载迁移配置
if [ -f ".env.migration" ]; then
    echo "Loading migration configuration..."
    set -a
    . ./.env.migration
    set +a
fi

# 设置默认值
AUTO_FIX_MIGRATIONS=${AUTO_FIX_MIGRATIONS:-true}
AUTO_CLEAN_FAILED_MIGRATIONS=${AUTO_CLEAN_FAILED_MIGRATIONS:-true}
ALLOW_SCHEMA_DIFF_MIGRATION=${ALLOW_SCHEMA_DIFF_MIGRATION:-true}
BACKUP_BEFORE_MIGRATION=${BACKUP_BEFORE_MIGRATION:-true}
MIGRATION_TIMEOUT=${MIGRATION_TIMEOUT:-300}
CONTINUE_ON_MIGRATION_FAILURE=${CONTINUE_ON_MIGRATION_FAILURE:-false}
VERBOSE_MIGRATION_LOGS=${VERBOSE_MIGRATION_LOGS:-true}
DANGEROUS_OPERATIONS=${DANGEROUS_OPERATIONS:-"DROP TABLE,DROP DATABASE,TRUNCATE,DELETE FROM"}
ALLOW_PRODUCTION_RESET=${ALLOW_PRODUCTION_RESET:-false}

echo "Migration configuration:"
echo "  AUTO_FIX_MIGRATIONS: $AUTO_FIX_MIGRATIONS"
echo "  AUTO_CLEAN_FAILED_MIGRATIONS: $AUTO_CLEAN_FAILED_MIGRATIONS"
echo "  ALLOW_SCHEMA_DIFF_MIGRATION: $ALLOW_SCHEMA_DIFF_MIGRATION"
echo "  NODE_ENV: ${NODE_ENV:-development}"

echo ""
echo "Loaded environment variables:"
env | grep -E "(DATABASE_URL|MOBILE_API_DATABASE_URL|NODE_ENV)" || echo "No relevant environment variables found"

# 函数：检查数据库连接
check_database_connection() {
    echo "=== Checking Database Connection ==="
    npx prisma db pull --print > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✓ Database connection successful"
        return 0
    else
        echo "✗ Database connection failed"
        return 1
    fi
}

# 函数：修复失败的迁移
fix_failed_migrations() {
    if [ "$AUTO_FIX_MIGRATIONS" != "true" ]; then
        echo "Auto-fix migrations disabled, skipping..."
        return 0
    fi

    echo "=== Checking for Failed Migrations ==="

    # 创建备份（如果启用）
    if [ "$BACKUP_BEFORE_MIGRATION" = "true" ]; then
        echo "Creating migration table backup..."
        psql "$DATABASE_URL" -c "CREATE TABLE IF NOT EXISTS _prisma_migrations_backup AS SELECT * FROM _prisma_migrations;" 2>/dev/null || true
    fi

    # 检查迁移状态
    MIGRATE_STATUS=$(timeout $MIGRATION_TIMEOUT npx prisma migrate status 2>&1)

    # 检查是否有失败的迁移
    if echo "$MIGRATE_STATUS" | grep -q "migrate found failed migrations"; then
        echo "⚠️  Found failed migrations, attempting to fix..."

        # 提取失败的迁移名称
        FAILED_MIGRATION=$(echo "$MIGRATE_STATUS" | grep "migration started at" | sed 's/.*The `\([^`]*\)`.*/\1/')

        if [ -n "$FAILED_MIGRATION" ]; then
            echo "Found failed migration: $FAILED_MIGRATION"

            # 尝试标记为已解决
            echo "Attempting to resolve failed migration..."
            npx prisma migrate resolve --applied "$FAILED_MIGRATION"

            if [ $? -eq 0 ]; then
                echo "✓ Successfully resolved failed migration: $FAILED_MIGRATION"
            elif [ "$AUTO_CLEAN_FAILED_MIGRATIONS" = "true" ]; then
                echo "⚠️  Failed to resolve migration automatically"
                echo "Attempting manual cleanup..."

                # 手动清理失败的迁移记录
                psql "$DATABASE_URL" -c "DELETE FROM \"_prisma_migrations\" WHERE migration_name = '$FAILED_MIGRATION';" 2>/dev/null

                if [ $? -eq 0 ]; then
                    echo "✓ Manually cleaned up failed migration record"
                else
                    echo "✗ Failed to clean up migration record"
                    return 1
                fi
            else
                echo "✗ Failed to resolve migration and auto-clean is disabled"
                return 1
            fi
        fi
    else
        echo "✓ No failed migrations found"
    fi

    return 0
}

# 函数：生成和应用 schema 差异迁移
apply_schema_diff() {
    if [ "$ALLOW_SCHEMA_DIFF_MIGRATION" != "true" ]; then
        echo "Schema diff migration disabled, skipping..."
        return 0
    fi

    echo "=== Checking Schema Differences ==="

    # 生成 schema 差异
    DIFF_OUTPUT=$(timeout $MIGRATION_TIMEOUT npx prisma migrate diff --from-url "$DATABASE_URL" --to-schema-datamodel prisma/schema.prisma --script 2>/dev/null)

    if [ $? -eq 0 ] && [ -n "$DIFF_OUTPUT" ]; then
        echo "Found schema differences, creating migration..."

        if [ "$VERBOSE_MIGRATION_LOGS" = "true" ]; then
            echo "Schema differences:"
            echo "$DIFF_OUTPUT"
        fi

        # 保存差异到临时文件
        echo "$DIFF_OUTPUT" > /tmp/schema_diff.sql

        # 检查差异是否安全（不包含危险操作）
        IFS=',' read -ra DANGEROUS_OPS <<< "$DANGEROUS_OPERATIONS"
        for op in "${DANGEROUS_OPS[@]}"; do
            if echo "$DIFF_OUTPUT" | grep -qi "$op"; then
                echo "⚠️  Detected potentially dangerous operation: $op"
                echo "Skipping automatic migration for safety"
                echo "Please review the migration manually:"
                cat /tmp/schema_diff.sql
                return 1
            fi
        done

        echo "Applying schema differences..."
        psql "$DATABASE_URL" -f /tmp/schema_diff.sql

        if [ $? -eq 0 ]; then
            echo "✓ Successfully applied schema differences"
            rm -f /tmp/schema_diff.sql
        else
            echo "✗ Failed to apply schema differences"
            if [ "$VERBOSE_MIGRATION_LOGS" = "true" ]; then
                echo "Failed SQL:"
                cat /tmp/schema_diff.sql
            fi
            return 1
        fi
    else
        echo "✓ No schema differences found"
    fi

    return 0
}

echo ""
echo "=== Generating Prisma Client ==="
npx prisma generate
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to generate Prisma client"
    exit 1
fi

echo ""
echo "=== Verifying Prisma Client ==="
if [ -d "node_modules/.prisma/client" ]; then
    echo "✓ Prisma client generated successfully"
    ls -la node_modules/.prisma/client/ | head -5
else
    echo "ERROR: Prisma client not found"
    exit 1
fi

echo ""
# 检查数据库连接
if ! check_database_connection; then
    echo "ERROR: Cannot connect to database"
    exit 1
fi

echo ""
# 修复失败的迁移
if ! fix_failed_migrations; then
    if [ "$CONTINUE_ON_MIGRATION_FAILURE" = "true" ]; then
        echo "⚠️  Failed to fix migrations, but continuing due to configuration"
    else
        echo "ERROR: Failed to fix migrations"
        exit 1
    fi
fi

echo ""
echo "=== Running Database Migrations ==="
timeout $MIGRATION_TIMEOUT npx prisma migrate deploy
MIGRATE_RESULT=$?

if [ $MIGRATE_RESULT -ne 0 ]; then
    echo "⚠️  Standard migration failed, attempting schema diff approach..."

    # 尝试应用 schema 差异
    if apply_schema_diff; then
        echo "✓ Successfully applied schema using diff approach"
    else
        echo "ERROR: All migration approaches failed"

        if [ "$CONTINUE_ON_MIGRATION_FAILURE" = "true" ]; then
            echo "⚠️  Continuing application startup despite migration failure"
        else
            # 运行修复脚本作为最后的尝试
            echo "Running advanced migration fix script..."
            if [ -f "/app/scripts/fix-migrations.sh" ]; then
                /app/scripts/fix-migrations.sh auto
                if [ $? -eq 0 ]; then
                    echo "✓ Advanced migration fix succeeded"
                else
                    echo "✗ Advanced migration fix failed"
                    exit 1
                fi
            else
                exit 1
            fi
        fi
    fi
else
    echo "✓ Standard migration completed successfully"
fi

echo ""
echo "=== Final Migration Status Check ==="
timeout 30 npx prisma migrate status
FINAL_STATUS=$?

if [ $FINAL_STATUS -ne 0 ]; then
    echo "⚠️  Migration status check failed"

    if [ "$CONTINUE_ON_MIGRATION_FAILURE" = "true" ]; then
        echo "Continuing application startup despite status check failure"
    else
        echo "ERROR: Migration status verification failed"
        exit 1
    fi
else
    echo "✓ All migrations are up to date"
fi

echo ""
echo "=== Starting Application ==="
echo "Application starting with the following configuration:"
echo "  NODE_ENV: ${NODE_ENV:-development}"
echo "  Database: Connected"
echo "  Migrations: $([ $FINAL_STATUS -eq 0 ] && echo "✓ Up to date" || echo "⚠️ Warning")"

npm run start
