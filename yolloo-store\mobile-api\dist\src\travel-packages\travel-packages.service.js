"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TravelPackagesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TravelPackagesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const rating_service_1 = require("../rating/rating.service");
let TravelPackagesService = TravelPackagesService_1 = class TravelPackagesService {
    prisma;
    ratingService;
    logger = new common_1.Logger(TravelPackagesService_1.name);
    constructor(prisma, ratingService) {
        this.prisma = prisma;
        this.ratingService = ratingService;
    }
    async getTravelPackages(query, ctx) {
        console.log('Context in getTravelPackages:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            const whereConditions = {
                status: 'ACTIVE',
                off_shelve: false,
                OR: [
                    { name: { contains: '旅游', mode: 'insensitive' } },
                    { name: { contains: 'travel', mode: 'insensitive' } },
                    { name: { contains: 'package', mode: 'insensitive' } },
                    { name: { contains: '套餐', mode: 'insensitive' } },
                    { description: { contains: '旅游', mode: 'insensitive' } },
                    { description: { contains: 'travel', mode: 'insensitive' } },
                    { description: { contains: 'package', mode: 'insensitive' } },
                    { description: { contains: '套餐', mode: 'insensitive' } },
                ],
            };
            if (query.destination) {
                whereConditions.AND = whereConditions.AND || [];
                whereConditions.AND.push({
                    OR: [
                        { country: { contains: query.destination, mode: 'insensitive' } },
                        { countryCode: { contains: query.destination, mode: 'insensitive' } },
                        { name: { contains: query.destination, mode: 'insensitive' } },
                    ],
                });
            }
            if (query.region) {
                whereConditions.AND = whereConditions.AND || [];
                whereConditions.AND.push({
                    OR: [
                        { name: { contains: query.region, mode: 'insensitive' } },
                        { description: { contains: query.region, mode: 'insensitive' } },
                    ],
                });
            }
            const total = await this.prisma.product.count({
                where: whereConditions,
            });
            if (total === 0) {
                return {
                    packages: [],
                    pagination: {
                        total: 0,
                        page: query.page,
                        pageSize: query.pageSize,
                        hasMore: false,
                    },
                    filters: {
                        regions: [],
                        durations: [],
                        dataSizes: [],
                    },
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            const skip = (query.page - 1) * query.pageSize;
            const products = await this.prisma.product.findMany({
                where: whereConditions,
                include: {
                    category: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    variants: {
                        select: {
                            id: true,
                            price: true,
                            currency: true,
                        },
                        orderBy: {
                            price: 'asc',
                        },
                    },
                    reviews: {
                        select: {
                            rating: true,
                        },
                    },
                },
                skip,
                take: query.pageSize,
                orderBy: this.buildOrderBy(query.sortBy, query.sortOrder),
            });
            const formattedPackages = products.map(product => this.formatProductAsPackage(product, ctx, isZh));
            return {
                packages: formattedPackages,
                pagination: {
                    total,
                    page: query.page,
                    pageSize: query.pageSize,
                    hasMore: skip + formattedPackages.length < total,
                },
                filters: {
                    destination: query.destination,
                    region: query.region,
                    duration: query.duration,
                    dataSize: query.dataSize,
                },
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching travel packages:', error);
            throw new Error('Failed to fetch travel packages');
        }
    }
    async getPackageById(packageId, ctx) {
        console.log('Context in getPackageById:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            const product = await this.prisma.product.findUnique({
                where: { id: packageId },
                include: {
                    category: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    variants: {
                        select: {
                            id: true,
                            price: true,
                            currency: true,
                        },
                        orderBy: {
                            price: 'asc',
                        },
                    },
                    reviews: {
                        select: {
                            rating: true,
                            comment: true,
                            createdAt: true,
                        },
                        orderBy: {
                            createdAt: 'desc',
                        },
                        take: 10,
                    },
                },
            });
            if (!product) {
                throw new Error('Travel package not found');
            }
            const packageDetails = this.formatProductAsDetailedPackage(product, ctx, isZh);
            return {
                package: packageDetails,
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching package details:', error);
            throw new Error('Failed to fetch travel package details');
        }
    }
    async createTravelOrder(orderData, ctx) {
        console.log('Context in createTravelOrder:', ctx);
        const isZh = ctx.language.startsWith('zh');
        const order = {
            id: `travel_${Date.now()}`,
            packageId: orderData.packageId,
            startDate: orderData.startDate,
            countries: orderData.countries,
            status: 'pending',
            statusText: isZh ? '待支付' : 'Pending Payment',
            createdAt: new Date().toISOString(),
            estimatedDeliveryTime: new Date(Date.now() + 10 * 60 * 1000).toISOString(),
        };
        return {
            order,
            message: isZh ? '旅游套餐订单创建成功，请完成支付' : 'Travel package order created successfully, please complete payment',
        };
    }
    async formatProductAsPackage(product, ctx, isZh) {
        const ratingData = await this.ratingService.calculateProductRating(product.id);
        const lowestPrice = product.variants.length > 0
            ? Math.min(...product.variants.map((v) => Number(v.price)))
            : Number(product.price);
        const currency = product.variants.length > 0
            ? product.variants[0].currency || ctx.currency
            : ctx.currency;
        let countries = [];
        let features = [];
        let duration = 7;
        try {
            const specs = typeof product.specifications === 'string'
                ? JSON.parse(product.specifications)
                : product.specifications;
            countries = specs?.countries || [];
            features = specs?.features || [];
            duration = specs?.duration || 7;
        }
        catch (error) {
            this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
        }
        if (countries.length === 0 && product.country) {
            countries = product.country.split(/[,;]/).map((c) => c.trim()).filter((c) => c);
        }
        if (features.length === 0) {
            features = [
                isZh ? '高速流量' : 'High-speed data',
                isZh ? '全国覆盖' : 'Nationwide coverage',
                isZh ? '即插即用' : 'Plug and play',
                isZh ? '24/7客服支持' : '24/7 customer support'
            ];
        }
        const destination = countries.length > 0 ? countries[0].toLowerCase() : 'unknown';
        const region = this.getRegionFromCountries(countries);
        return {
            id: product.id,
            name: product.name,
            description: product.description,
            destination: destination,
            region: region,
            countries: countries,
            duration: duration,
            dataSize: product.dataSize ? this.formatDataSize(product.dataSize) : 'unlimited',
            price: lowestPrice,
            originalPrice: lowestPrice * 1.2,
            currency: currency,
            features: features,
            networkType: '4G/5G',
            coverage: countries.length > 0 ? (isZh ? `${countries.join('、')}全境` : `All of ${countries.join(', ')}`) : (isZh ? '全球覆盖' : 'Global coverage'),
            imageUrl: product.images && product.images.length > 0
                ? product.images[0]
                : '/images/defaults/travel-package-placeholder.jpg',
            isPopular: ratingData.averageRating >= 4.5,
            rating: ratingData.averageRating,
            reviewCount: ratingData.totalReviews,
        };
    }
    async formatProductAsDetailedPackage(product, ctx, isZh) {
        const basicPackage = await this.formatProductAsPackage(product, ctx, isZh);
        return {
            ...basicPackage,
            detailedInfo: {
                activation: isZh ? '到达目的地后自动激活' : 'Auto-activation upon arrival at destination',
                validity: isZh ? `激活后${basicPackage.duration}天有效` : `Valid for ${basicPackage.duration} days after activation`,
                speed: isZh ? '下载速度最高150Mbps' : 'Download speed up to 150Mbps',
                compatibility: isZh ? '支持所有eSIM设备' : 'Compatible with all eSIM devices',
            },
            includedServices: [
                isZh ? '免费接收短信' : 'Free incoming SMS',
                isZh ? '热点分享功能' : 'Hotspot sharing',
                isZh ? '多设备支持' : 'Multi-device support',
                isZh ? '实时流量查询' : 'Real-time data usage check',
            ],
            variants: product.variants.map((variant) => ({
                id: variant.id,
                price: Number(variant.price),
                currency: variant.currency,
            })),
            reviews: product.reviews.map((review) => ({
                rating: review.rating,
                comment: review.comment,
                createdAt: review.createdAt,
            })),
        };
    }
    buildOrderBy(sortBy, sortOrder) {
        const order = sortOrder || 'asc';
        switch (sortBy) {
            case 'price':
                return { price: order };
            case 'rating':
                return { createdAt: order };
            case 'name':
                return { name: order };
            default:
                return { createdAt: 'desc' };
        }
    }
    getRegionFromCountries(countries) {
        if (countries.length === 0)
            return 'global';
        const asianCountries = ['japan', 'korea', 'china', 'thailand', 'singapore', 'malaysia', 'vietnam', 'philippines'];
        const europeanCountries = ['france', 'germany', 'italy', 'spain', 'uk', 'netherlands', 'switzerland', 'austria'];
        const americanCountries = ['usa', 'canada', 'mexico', 'brazil', 'argentina'];
        const firstCountry = countries[0].toLowerCase();
        if (asianCountries.some(country => firstCountry.includes(country))) {
            return 'asia';
        }
        else if (europeanCountries.some(country => firstCountry.includes(country))) {
            return 'europe';
        }
        else if (americanCountries.some(country => firstCountry.includes(country))) {
            return 'america';
        }
        return 'global';
    }
    formatDataSize(dataSize) {
        if (dataSize >= 1024) {
            const sizeInGB = dataSize / 1024;
            return sizeInGB % 1 === 0 ? `${sizeInGB}GB` : `${sizeInGB.toFixed(1)}GB`;
        }
        else {
            return `${dataSize}MB`;
        }
    }
};
TravelPackagesService = TravelPackagesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        rating_service_1.RatingService])
], TravelPackagesService);
exports.TravelPackagesService = TravelPackagesService;
//# sourceMappingURL=travel-packages.service.js.map