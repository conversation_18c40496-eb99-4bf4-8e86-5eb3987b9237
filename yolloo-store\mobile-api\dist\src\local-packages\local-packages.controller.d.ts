import { LocalPackagesService } from './local-packages.service';
import { LocalPackagesQueryDto, LocalPackageOrderDto } from './dto/local-packages-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class LocalPackagesController {
    private readonly localPackagesService;
    constructor(localPackagesService: LocalPackagesService);
    getLocalPackages(query: LocalPackagesQueryDto, ctx: RequestContext): Promise<{
        packages: {
            id: any;
            name: any;
            description: any;
            city: string;
            province: string;
            planType: any;
            dataSize: string;
            price: number;
            originalPrice: number;
            currency: any;
            features: string[];
            coverage: string;
            networkType: string;
            validity: string;
            imageUrl: any;
            isPopular: boolean;
            rating: number;
            reviewCount: any;
        }[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
        filters: {
            cities: {
                id: string;
                name: string;
            }[];
            planTypes: {
                value: string;
                label: string;
            }[];
            dataSizes: {
                value: string;
                label: string;
            }[];
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getPackageById(packageId: string, ctx: RequestContext): Promise<{
        detailedInfo: {
            activation: string;
            coverage: string;
            speed: string;
            usage: string;
        };
        restrictions: string[];
        variants: any;
        reviews: any;
        id: any;
        name: any;
        description: any;
        city: string;
        province: string;
        planType: any;
        dataSize: string;
        price: number;
        originalPrice: number;
        currency: any;
        features: string[];
        coverage: string;
        networkType: string;
        validity: string;
        imageUrl: any;
        isPopular: boolean;
        rating: number;
        reviewCount: any;
    }>;
    createLocalOrder(orderData: LocalPackageOrderDto, ctx: RequestContext): Promise<{
        order: {
            id: string;
            packageId: string;
            city: string | undefined;
            province: string | undefined;
            status: string;
            statusText: string;
            createdAt: string;
            estimatedActivationTime: string;
        };
        message: string;
    }>;
}
