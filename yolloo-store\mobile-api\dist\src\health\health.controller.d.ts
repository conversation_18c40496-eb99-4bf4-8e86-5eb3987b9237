import { PrismaService } from '../prisma.service';
export declare class HealthController {
    private readonly prismaService;
    constructor(prismaService: PrismaService);
    checkHealth(): Promise<{
        status: string;
        timestamp: string;
        database: string;
        error?: undefined;
    } | {
        status: string;
        timestamp: string;
        database: string;
        error: any;
    }>;
}
