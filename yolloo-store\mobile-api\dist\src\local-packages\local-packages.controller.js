"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalPackagesController = void 0;
const common_1 = require("@nestjs/common");
const local_packages_service_1 = require("./local-packages.service");
const local_packages_query_dto_1 = require("./dto/local-packages-query.dto");
const request_context_decorator_1 = require("../common/decorators/request-context.decorator");
const public_decorator_1 = require("../common/decorators/public.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let LocalPackagesController = class LocalPackagesController {
    localPackagesService;
    constructor(localPackagesService) {
        this.localPackagesService = localPackagesService;
    }
    getLocalPackages(query, ctx) {
        return this.localPackagesService.getLocalPackages(query, ctx);
    }
    getPackageById(packageId, ctx) {
        return this.localPackagesService.getPackageById(packageId, ctx);
    }
    createLocalOrder(orderData, ctx) {
        return this.localPackagesService.createLocalOrder(orderData, ctx);
    }
};
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [local_packages_query_dto_1.LocalPackagesQueryDto, Object]),
    __metadata("design:returntype", void 0)
], LocalPackagesController.prototype, "getLocalPackages", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(':packageId'),
    __param(0, (0, common_1.Param)('packageId')),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], LocalPackagesController.prototype, "getPackageById", null);
__decorate([
    (0, common_1.Post)('order'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [local_packages_query_dto_1.LocalPackageOrderDto, Object]),
    __metadata("design:returntype", void 0)
], LocalPackagesController.prototype, "createLocalOrder", null);
LocalPackagesController = __decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('local-packages'),
    __metadata("design:paramtypes", [local_packages_service_1.LocalPackagesService])
], LocalPackagesController);
exports.LocalPackagesController = LocalPackagesController;
//# sourceMappingURL=local-packages.controller.js.map