import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma.service';
import { ReverseGeocodeDto } from './dto/reverse-geocode.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class LocationService {
    private readonly httpService;
    private readonly configService;
    private readonly prisma;
    private readonly logger;
    private readonly googleMapsApiKey;
    constructor(httpService: HttpService, configService: ConfigService, prisma: PrismaService);
    reverseGeocode(dto: ReverseGeocodeDto, ctx: RequestContext): Promise<{
        locations: {
            formattedAddress: any;
            placeId: any;
            country: string;
            countryCode: string;
            administrativeArea1: string;
            administrativeArea2: string;
            locality: string;
            coordinates: {
                lat: any;
                lng: any;
            };
        }[];
    } | {
        locations: {
            formattedAddress: string;
            placeId: string;
            country: string;
            countryCode: string;
            administrativeArea1: string;
            administrativeArea2: string;
            locality: string;
            geometry: {
                lat: number;
                lng: number;
            };
        }[];
        error?: undefined;
    } | {
        locations: never[];
        error: string;
    }>;
    private performRealReverseGeocode;
    private formatGeocodingResults;
    getNearbyPlaces(lat: number, lng: number, radius?: number, type?: string, ctx?: RequestContext): Promise<{
        places: any;
        status: string;
    } | {
        places: never[];
        error: string;
        status?: undefined;
        message?: undefined;
    } | {
        places: never[];
        status: string;
        message: string;
        error?: undefined;
    }>;
    private performRealNearbySearch;
    private getLocationFromDatabase;
}
