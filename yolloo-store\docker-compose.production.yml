services:

  # 主应用服务 (负责数据库迁移)
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yolloo-app-production
    ports:
      - "8000:8000"
    env_file:
      - .env.production
    restart: always
    extra_hosts:
      - "openapi.vcs3-distribution.testing:***********"
      - "boss-server.vcs3-distribution.testing:***********"
      - "postgresql.postgresql:*************"
      - "redis-master.redis:*************"
      - "openapi.simmesh.com:***********"
      - "bs2-openmsg.simmesh.com:***********"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 移动 API 服务 (等待主应用完成迁移后启动)
  mobile-api:
    build:
      context: .
      dockerfile: mobile-api/Dockerfile
    container_name: yolloo-mobile-api-production
    ports:
      - "4000:4000"
    env_file:
      - .env.production
    restart: always
    extra_hosts:
      - "openapi.vcs3-distribution.testing:***********"
      - "boss-server.vcs3-distribution.testing:***********"
      - "postgresql.postgresql:*************"
      - "redis-master.redis:*************"
      - "openapi.simmesh.com:***********"
      - "bs2-openmsg.simmesh.com:***********"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      app:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:4000/api/mobile/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
