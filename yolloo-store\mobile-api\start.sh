#!/bin/sh

echo "=== Mobile API Startup Script ==="
echo "Loaded environment variables:"
env | grep -E "(DATABASE_URL|MOBILE_API_DATABASE_URL|NODE_ENV)" || echo "No relevant environment variables found"

echo ""
echo "=== Generating Prisma Client ==="
npx prisma generate
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to generate Prisma client"
    exit 1
fi

echo ""
echo "=== Verifying Prisma Client ==="
if [ -d "node_modules/.prisma/client" ]; then
    echo "✓ Prisma client generated successfully"
    ls -la node_modules/.prisma/client/ | head -5
else
    echo "ERROR: Prisma client not found"
    exit 1
fi

echo ""
echo "=== Database migrations are managed by main application ==="
echo "=== Skipping main application wait (assuming it's ready) ==="

echo "=== Checking database connection ==="
npx prisma db execute --stdin <<< "SELECT 1;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Database connection successful"
else
    echo "WARNING: Database connection failed, but continuing startup..."
fi

echo ""
echo "=== Starting Application ==="
npm run start
