"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeographyController = void 0;
const common_1 = require("@nestjs/common");
const geography_service_1 = require("./geography.service");
const geography_query_dto_1 = require("./dto/geography-query.dto");
const public_decorator_1 = require("../common/decorators/public.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const request_context_decorator_1 = require("../common/decorators/request-context.decorator");
let GeographyController = class GeographyController {
    geographyService;
    constructor(geographyService) {
        this.geographyService = geographyService;
    }
    getContinents(ctx) {
        return this.geographyService.getContinents(ctx);
    }
    getCountriesByContinent(continent, ctx) {
        return this.geographyService.getCountriesByContinent(continent, ctx);
    }
    getProductsByCountry(countryCode, query, ctx) {
        const dto = { ...query, countryCode };
        return this.geographyService.getProductsByCountry(dto, ctx);
    }
    getAllProducts(query, ctx) {
        return this.geographyService.getProductsByCountry(query, ctx);
    }
    getProductFilters(ctx) {
        return this.geographyService.getProductFilters(ctx);
    }
};
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('continents'),
    __param(0, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], GeographyController.prototype, "getContinents", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('continents/:continent/countries'),
    __param(0, (0, common_1.Param)('continent')),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], GeographyController.prototype, "getCountriesByContinent", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('countries/:countryCode/products'),
    __param(0, (0, common_1.Param)('countryCode')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], GeographyController.prototype, "getProductsByCountry", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('products'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [geography_query_dto_1.ProductsByCountryDto, Object]),
    __metadata("design:returntype", void 0)
], GeographyController.prototype, "getAllProducts", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('filters'),
    __param(0, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], GeographyController.prototype, "getProductFilters", null);
GeographyController = __decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('geography'),
    __metadata("design:paramtypes", [geography_service_1.GeographyService])
], GeographyController);
exports.GeographyController = GeographyController;
//# sourceMappingURL=geography.controller.js.map