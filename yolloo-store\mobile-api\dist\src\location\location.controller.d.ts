import { LocationService } from './location.service';
import { ReverseGeocodeDto } from './dto/reverse-geocode.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class LocationController {
    private readonly locationService;
    constructor(locationService: LocationService);
    reverseGeocode(query: ReverseGeocodeDto, ctx: RequestContext): Promise<{
        locations: {
            formattedAddress: any;
            placeId: any;
            country: string;
            countryCode: string;
            administrativeArea1: string;
            administrativeArea2: string;
            locality: string;
            coordinates: {
                lat: any;
                lng: any;
            };
        }[];
    } | {
        locations: {
            formattedAddress: string;
            placeId: string;
            country: string;
            countryCode: string;
            administrativeArea1: string;
            administrativeArea2: string;
            locality: string;
            geometry: {
                lat: number;
                lng: number;
            };
        }[];
        error?: undefined;
    } | {
        locations: never[];
        error: string;
    }>;
}
