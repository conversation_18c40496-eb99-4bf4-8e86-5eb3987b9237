"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MobileRechargeController = void 0;
const common_1 = require("@nestjs/common");
const mobile_recharge_service_1 = require("./mobile-recharge.service");
const recharge_query_dto_1 = require("./dto/recharge-query.dto");
const request_context_decorator_1 = require("../common/decorators/request-context.decorator");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const public_decorator_1 = require("../common/decorators/public.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let MobileRechargeController = class MobileRechargeController {
    mobileRechargeService;
    constructor(mobileRechargeService) {
        this.mobileRechargeService = mobileRechargeService;
    }
    getRechargeOptions(query, ctx) {
        return this.mobileRechargeService.getRechargeOptions(query, ctx);
    }
    createRechargeOrder(orderData, ctx) {
        return this.mobileRechargeService.createRechargeOrder(orderData, ctx);
    }
    getRechargeHistory(userId, query, ctx) {
        return this.mobileRechargeService.getRechargeHistory(userId, query, ctx);
    }
};
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('options'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [recharge_query_dto_1.RechargeQueryDto, Object]),
    __metadata("design:returntype", void 0)
], MobileRechargeController.prototype, "getRechargeOptions", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('order'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [recharge_query_dto_1.RechargeOrderDto, Object]),
    __metadata("design:returntype", void 0)
], MobileRechargeController.prototype, "createRechargeOrder", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('history'),
    __param(0, (0, current_user_decorator_1.CurrentUser)('id')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, recharge_query_dto_1.RechargeQueryDto, Object]),
    __metadata("design:returntype", void 0)
], MobileRechargeController.prototype, "getRechargeHistory", null);
MobileRechargeController = __decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('mobile-recharge'),
    __metadata("design:paramtypes", [mobile_recharge_service_1.MobileRechargeService])
], MobileRechargeController);
exports.MobileRechargeController = MobileRechargeController;
//# sourceMappingURL=mobile-recharge.controller.js.map