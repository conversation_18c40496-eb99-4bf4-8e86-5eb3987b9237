"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LocationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../prisma.service");
let LocationService = LocationService_1 = class LocationService {
    httpService;
    configService;
    prisma;
    logger = new common_1.Logger(LocationService_1.name);
    googleMapsApiKey;
    constructor(httpService, configService, prisma) {
        this.httpService = httpService;
        this.configService = configService;
        this.prisma = prisma;
        this.googleMapsApiKey = this.configService.get('GOOGLE_MAPS_API_KEY');
        if (!this.googleMapsApiKey) {
            this.logger.warn('GOOGLE_MAPS_API_KEY is not set. Geocoding service will not work properly.');
        }
    }
    async reverseGeocode(dto, ctx) {
        try {
            this.logger.log(`接收到反向地理编码请求，坐标: ${dto.lat},${dto.lng}`);
            if (this.googleMapsApiKey) {
                try {
                    const realResult = await this.performRealReverseGeocode(dto, ctx);
                    if (realResult) {
                        this.logger.log('使用Google Maps API返回真实地理编码数据');
                        return realResult;
                    }
                }
                catch (error) {
                    this.logger.warn('Google Maps API调用失败，使用fallback数据:', error.message);
                }
            }
            this.logger.log('尝试从数据库获取地理编码数据');
            const location = await this.getLocationFromDatabase(dto.lat, dto.lng, ctx);
            if (location) {
                return { locations: [location] };
            }
            this.logger.log('无法获取地理编码数据');
            return {
                locations: [],
                error: 'Unable to geocode location'
            };
        }
        catch (error) {
            this.logger.error('反向地理编码处理过程中发生错误', error);
            return {
                locations: [{
                        formattedAddress: '位置数据处理错误',
                        placeId: '',
                        country: '未知',
                        countryCode: '',
                        administrativeArea1: '',
                        administrativeArea2: '',
                        locality: '',
                        coordinates: {
                            lat: dto.lat,
                            lng: dto.lng,
                        },
                    }]
            };
        }
    }
    async performRealReverseGeocode(dto, ctx) {
        try {
            const language = ctx?.language?.startsWith('zh') ? 'zh-CN' : 'en';
            const url = 'https://maps.googleapis.com/maps/api/geocode/json';
            const params = {
                latlng: `${dto.lat},${dto.lng}`,
                key: this.googleMapsApiKey,
                language: language,
                result_type: 'street_address|route|neighborhood|locality|administrative_area_level_2|administrative_area_level_1|country',
            };
            this.logger.log(`调用Google Maps API: ${url}`);
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(url, { params, timeout: 5000 }));
            if (response.data.status === 'OK' && response.data.results.length > 0) {
                return this.formatGeocodingResults(response.data.results);
            }
            else {
                this.logger.warn(`Google Maps API返回状态: ${response.data.status}`);
                return null;
            }
        }
        catch (error) {
            this.logger.error('Google Maps API调用失败:', error);
            throw error;
        }
    }
    formatGeocodingResults(results) {
        return {
            locations: results.map((result) => {
                const addressComponents = result.address_components || [];
                const location = {
                    formattedAddress: result.formatted_address,
                    placeId: result.place_id,
                    country: '',
                    countryCode: '',
                    administrativeArea1: '',
                    administrativeArea2: '',
                    locality: '',
                    coordinates: {
                        lat: result.geometry?.location?.lat,
                        lng: result.geometry?.location?.lng,
                    },
                };
                for (const component of addressComponents) {
                    const types = component.types || [];
                    if (types.includes('country')) {
                        location.country = component.long_name;
                        location.countryCode = component.short_name;
                    }
                    else if (types.includes('administrative_area_level_1')) {
                        location.administrativeArea1 = component.long_name;
                    }
                    else if (types.includes('administrative_area_level_2')) {
                        location.administrativeArea2 = component.long_name;
                    }
                    else if (types.includes('locality')) {
                        location.locality = component.long_name;
                    }
                }
                return location;
            }),
        };
    }
    async getNearbyPlaces(lat, lng, radius = 1000, type, ctx) {
        try {
            this.logger.log(`获取附近地点请求，坐标: ${lat},${lng}, 半径: ${radius}m`);
            if (this.googleMapsApiKey) {
                try {
                    const realResult = await this.performRealNearbySearch(lat, lng, radius, type, ctx);
                    if (realResult) {
                        this.logger.log('使用Google Maps API返回真实附近地点数据');
                        return realResult;
                    }
                }
                catch (error) {
                    this.logger.warn('Google Maps Nearby Search API调用失败，使用fallback数据:', error.message);
                }
            }
            this.logger.log('无法获取附近地点数据');
            return {
                places: [],
                error: 'Unable to fetch nearby places'
            };
        }
        catch (error) {
            this.logger.error('获取附近地点过程中发生错误', error);
            return {
                places: [],
                status: 'error',
                message: '获取附近地点失败',
            };
        }
    }
    async performRealNearbySearch(lat, lng, radius, type, ctx) {
        try {
            const language = ctx?.language?.startsWith('zh') ? 'zh-CN' : 'en';
            const url = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
            const params = {
                location: `${lat},${lng}`,
                radius: radius,
                key: this.googleMapsApiKey,
                language: language,
            };
            if (type) {
                params.type = type;
            }
            this.logger.log(`调用Google Places API: ${url}`);
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(url, { params, timeout: 10000 }));
            if (response.data.status === 'OK') {
                return {
                    places: response.data.results.map((place) => ({
                        placeId: place.place_id,
                        name: place.name,
                        vicinity: place.vicinity,
                        types: place.types,
                        rating: place.rating,
                        priceLevel: place.price_level,
                        coordinates: {
                            lat: place.geometry.location.lat,
                            lng: place.geometry.location.lng,
                        },
                        openNow: place.opening_hours?.open_now,
                        photos: place.photos?.map((photo) => ({
                            reference: photo.photo_reference,
                            width: photo.width,
                            height: photo.height,
                        })) || [],
                    })),
                    status: 'success',
                };
            }
            else {
                this.logger.warn(`Google Places API返回状态: ${response.data.status}`);
                return null;
            }
        }
        catch (error) {
            this.logger.error('Google Places API调用失败:', error);
            throw error;
        }
    }
    async getLocationFromDatabase(lat, lng, ctx) {
        try {
            const isZh = ctx?.language?.startsWith('zh');
            let countryCode = null;
            if (lat >= 18 && lat <= 53 && lng >= 73 && lng <= 135) {
                countryCode = 'CN';
            }
            else if (lat >= 24 && lat <= 49 && lng >= -125 && lng <= -66) {
                countryCode = 'US';
            }
            else if (lat >= 24 && lat <= 46 && lng >= 123 && lng <= 146) {
                countryCode = 'JP';
            }
            if (countryCode) {
                const country = await this.prisma.country.findUnique({
                    where: { code: countryCode },
                    include: { continent: true },
                });
                if (country) {
                    return {
                        formattedAddress: isZh ? `${country.nameZh}` : `${country.nameEn}`,
                        placeId: `db-place-${country.code}`,
                        country: isZh ? country.nameZh : country.nameEn,
                        countryCode: country.code,
                        administrativeArea1: isZh ? country.nameZh : country.nameEn,
                        administrativeArea2: '',
                        locality: isZh ? country.nameZh : country.nameEn,
                        geometry: {
                            lat: lat,
                            lng: lng,
                        },
                    };
                }
            }
            return null;
        }
        catch (error) {
            this.logger.error('Error fetching location from database:', error);
            return null;
        }
    }
};
LocationService = LocationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService,
        config_1.ConfigService,
        prisma_service_1.PrismaService])
], LocationService);
exports.LocationService = LocationService;
//# sourceMappingURL=location.service.js.map