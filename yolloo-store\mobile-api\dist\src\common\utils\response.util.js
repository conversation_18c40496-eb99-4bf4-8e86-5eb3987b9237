"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseUtil = void 0;
const app_constants_1 = require("../constants/app.constants");
class ResponseUtil {
    static createPaginationResponse(total, page = app_constants_1.DEFAULT_PAGINATION.PAGE, pageSize = app_constants_1.DEFAULT_PAGINATION.PAGE_SIZE) {
        return {
            total,
            page,
            pageSize,
            hasMore: (page - 1) * pageSize + pageSize < total,
        };
    }
    static createContextResponse(ctx) {
        return {
            language: ctx.language,
            theme: ctx.theme,
            currency: ctx.currency,
        };
    }
    static createListResponse(items, total, page, pageSize, ctx, additionalData) {
        return {
            ...additionalData,
            [Array.isArray(items) ? this.getItemsKey(items) : 'data']: items,
            pagination: this.createPaginationResponse(total, page, pageSize),
            context: this.createContextResponse(ctx),
        };
    }
    static createSuccessResponse(data, message, ctx) {
        const response = { data };
        if (message) {
            response.message = message;
        }
        if (ctx) {
            response.context = this.createContextResponse(ctx);
        }
        return response;
    }
    static getItemsKey(items) {
        if (items.length === 0)
            return 'items';
        const firstItem = items[0];
        if (firstItem.boosterType !== undefined)
            return 'boosters';
        if (firstItem.planType !== undefined)
            return 'packages';
        if (firstItem.operator !== undefined)
            return 'rechargeOptions';
        if (firstItem.destination !== undefined)
            return 'packages';
        return 'items';
    }
}
exports.ResponseUtil = ResponseUtil;
//# sourceMappingURL=response.util.js.map