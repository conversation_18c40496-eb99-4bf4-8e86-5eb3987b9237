"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EsimsController = void 0;
const common_1 = require("@nestjs/common");
const cards_service_1 = require("./cards.service");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let EsimsController = class EsimsController {
    cardsService;
    constructor(cardsService) {
        this.cardsService = cardsService;
    }
    activateEsim(user, esimId) {
        if (!user) {
            throw new common_1.BadRequestException('User not authenticated');
        }
        if (!esimId || esimId.trim() === '') {
            throw new common_1.BadRequestException('eSIM ID is required');
        }
        return this.cardsService.activateEsim(user.id, esimId);
    }
};
__decorate([
    (0, common_1.Post)(':esimId/activate'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('esimId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], EsimsController.prototype, "activateEsim", null);
EsimsController = __decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('esims'),
    __metadata("design:paramtypes", [cards_service_1.CardsService])
], EsimsController);
exports.EsimsController = EsimsController;
//# sourceMappingURL=esims.controller.js.map