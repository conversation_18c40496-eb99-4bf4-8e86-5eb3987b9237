import { CardsService } from './cards.service';
import { RegisterCardDto } from './dto/register-card.dto';
import { AddEsimDto } from './dto/add-esim.dto';
export declare class CardsController {
    private readonly cardsService;
    constructor(cardsService: CardsService);
    getUserCards(user: any): Promise<{
        cards: {
            id: string;
            number: string;
            status: string;
            type: string;
            customName: string | null;
            activationDate: string | undefined;
            expiryDate: string | undefined;
            esimCount: number;
        }[];
    }>;
    getCardById(user: any, cardId: string): Promise<{
        id: string;
        number: string;
        status: string;
        type: string;
        customName: string | null;
        activationDate: string | null;
        expiryDate: string | null;
        esims: {
            id: string;
            iccid: string;
            status: string;
            activationDate: string | null;
            expiryDate: string | null;
            product: {
                id: string;
                name: string;
                description: string;
            } | null;
        }[];
    }>;
    registerCard(user: any, registerCardDto: RegisterCardDto): Promise<{
        id: string;
        number: string;
        status: string;
        message: string;
    }>;
    activateCard(user: any, cardId: string): Promise<{
        id: string;
        number: string;
        status: string;
        activationDate: string | null;
        expiryDate: string | null;
        message: string;
    }>;
    addEsimToCard(user: any, cardId: string, addEsimDto: AddEsimDto): Promise<{
        id: string;
        iccid: string;
        status: string;
        message: string;
    }>;
}
