"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerUtil = void 0;
const date_utils_1 = require("./date.utils");
class LoggerUtil {
    static logContext(methodName, ctx) {
        console.log(`[${methodName}] Context:`, {
            language: ctx.language,
            theme: ctx.theme,
            currency: ctx.currency,
            timestamp: date_utils_1.DateFormatter.iso(new Date()),
        });
    }
    static logRequest(methodName, params) {
        console.log(`[${methodName}] Request:`, {
            params,
            timestamp: date_utils_1.DateFormatter.iso(new Date()),
        });
    }
    static logError(methodName, error) {
        console.error(`[${methodName}] Error:`, {
            error: error.message || error,
            stack: error.stack,
            timestamp: date_utils_1.DateFormatter.iso(new Date()),
        });
    }
}
exports.LoggerUtil = LoggerUtil;
//# sourceMappingURL=logger.util.js.map