import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

export async function GET() {
  try {
    // 尝试连接数据库
    await prisma.user.findFirst({ take: 1 });
    
    return NextResponse.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: 'connected',
      service: 'main-app'
    });
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      service: 'main-app',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 503 });
  }
}
