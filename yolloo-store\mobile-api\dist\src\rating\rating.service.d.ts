import { PrismaService } from '../prisma.service';
export declare class RatingService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    calculateProductRating(productId: string): Promise<{
        averageRating: number;
        totalReviews: number;
        ratingDistribution: {
            1: number;
            2: number;
            3: number;
            4: number;
            5: number;
        };
    }>;
    getProductStatistics(productId: string): Promise<{
        totalOrders: number;
        totalViews: number;
        reviews: {
            id: string;
            rating: number;
            comment: string;
            createdAt: Date;
            user: {
                name: string;
                image: string;
            };
        }[];
        averageRating: number;
        totalReviews: number;
        ratingDistribution: {
            1: number;
            2: number;
            3: number;
            4: number;
            5: number;
        };
    }>;
    updateProductPopularity(productId: string): Promise<{
        popularityScore: number;
        isPopular: boolean;
    }>;
    private calculatePopularityScore;
    getTopRatedProducts(limit?: number, categoryId?: string): Promise<{
        averageRating: number;
        totalReviews: number;
        totalOrders: number;
        reviews: {
            rating: number;
        }[];
        _count: {
            orderItems: number;
            reviews: number;
        };
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        country: string | null;
        status: import(".prisma/client").ProductStatus;
        description: string;
        websiteDescription: string;
        price: number;
        images: string[];
        categoryId: string;
        stock: number;
        specifications: import(".prisma/client").Prisma.JsonValue;
        sku: string;
        requiredUID: boolean;
        mcc: string | null;
        off_shelve: boolean;
        dataSize: number | null;
        planType: string | null;
        countryCode: string | null;
        odooLastSyncAt: Date | null;
        popularityScore: number | null;
        isPopular: boolean;
    }[]>;
    getMostPopularProducts(limit?: number, categoryId?: string): Promise<({
        reviews: {
            rating: number;
        }[];
        _count: {
            orderItems: number;
            reviews: number;
        };
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        name: string;
        description: string;
        websiteDescription: string;
        price: number;
        images: string[];
        categoryId: string;
        stock: number;
        specifications: import(".prisma/client").Prisma.JsonValue;
        status: import(".prisma/client").ProductStatus;
        sku: string;
        requiredUID: boolean;
        createdAt: Date;
        updatedAt: Date;
        mcc: string | null;
        off_shelve: boolean;
        dataSize: number | null;
        planType: string | null;
        country: string | null;
        countryCode: string | null;
        odooLastSyncAt: Date | null;
        popularityScore: number | null;
        isPopular: boolean;
    }, unknown> & {})[]>;
    recordProductView(productId: string, userId?: string, ipAddress?: string, userAgent?: string): Promise<void>;
    getUserRecommendations(userId: string, limit?: number): Promise<{
        averageRating: number;
        totalReviews: number;
        totalOrders: number;
        reviews: {
            rating: number;
        }[];
        _count: {
            orderItems: number;
            reviews: number;
        };
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        country: string | null;
        status: import(".prisma/client").ProductStatus;
        description: string;
        websiteDescription: string;
        price: number;
        images: string[];
        categoryId: string;
        stock: number;
        specifications: import(".prisma/client").Prisma.JsonValue;
        sku: string;
        requiredUID: boolean;
        mcc: string | null;
        off_shelve: boolean;
        dataSize: number | null;
        planType: string | null;
        countryCode: string | null;
        odooLastSyncAt: Date | null;
        popularityScore: number | null;
        isPopular: boolean;
    }[]>;
    batchUpdateProductPopularity(): Promise<{
        updatedCount: number;
        totalProducts: number;
    }>;
    getProductRecommendations(productId: string, limit?: number): Promise<{
        averageRating: number;
        totalReviews: number;
        totalOrders: number;
        reviews: {
            rating: number;
        }[];
        _count: {
            orderItems: number;
            reviews: number;
        };
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        country: string | null;
        status: import(".prisma/client").ProductStatus;
        description: string;
        websiteDescription: string;
        price: number;
        images: string[];
        categoryId: string;
        stock: number;
        specifications: import(".prisma/client").Prisma.JsonValue;
        sku: string;
        requiredUID: boolean;
        mcc: string | null;
        off_shelve: boolean;
        dataSize: number | null;
        planType: string | null;
        countryCode: string | null;
        odooLastSyncAt: Date | null;
        popularityScore: number | null;
        isPopular: boolean;
    }[]>;
}
