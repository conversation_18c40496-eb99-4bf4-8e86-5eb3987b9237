import { PrismaService } from '../prisma.service';
import { RegisterCardDto } from './dto/register-card.dto';
import { AddEsimDto } from './dto/add-esim.dto';
export declare class CardsService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getUserCards(userId: string): Promise<{
        cards: {
            id: string;
            number: string;
            status: string;
            type: string;
            customName: string | null;
            activationDate: string | undefined;
            expiryDate: string | undefined;
            esimCount: number;
        }[];
    }>;
    getCardById(userId: string, cardId: string): Promise<{
        id: string;
        number: string;
        status: string;
        type: string;
        customName: string | null;
        activationDate: string | null;
        expiryDate: string | null;
        esims: {
            id: string;
            iccid: string;
            status: string;
            activationDate: string | null;
            expiryDate: string | null;
            product: {
                id: string;
                name: string;
                description: string;
            } | null;
        }[];
    }>;
    registerCard(userId: string, registerCardDto: RegisterCardDto): Promise<{
        id: string;
        number: string;
        status: string;
        message: string;
    }>;
    activateCard(userId: string, cardId: string): Promise<{
        id: string;
        number: string;
        status: string;
        activationDate: string | null;
        expiryDate: string | null;
        message: string;
    }>;
    addEsimToCard(userId: string, cardId: string, addEsimDto: AddEsimDto): Promise<{
        id: string;
        iccid: string;
        status: string;
        message: string;
    }>;
    activateEsim(userId: string, esimId: string): Promise<{
        id: string;
        status: string;
        activationDate: string | null;
        expiryDate: string | null;
        qrCodeUrl: string;
        message: string;
    }>;
}
