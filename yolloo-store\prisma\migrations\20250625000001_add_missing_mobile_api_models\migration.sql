-- CreateEnum (with conditional check)
DO $$ BEGIN
    CREATE TYPE "RechargeStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- CreateEnum (with conditional check)
DO $$ BEGIN
    CREATE TYPE "RefundStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- CreateEnum (with conditional check)
DO $$ BEGIN
    CREATE TYPE "UploadCategory" AS ENUM ('products', 'categories', 'users', 'rewards', 'temp');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- CreateTable (with conditional check)
CREATE TABLE IF NOT EXISTS "Refund" (
    "id" TEXT NOT NULL,
    "orderId" TEXT,
    "paymentId" TEXT,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "reason" TEXT,
    "status" "RefundStatus" NOT NULL DEFAULT 'PENDING',
    "processedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Refund_pkey" PRIMARY KEY ("id")
);

-- AlterTable (with conditional checks)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'RechargeHistory' AND column_name = 'operatorId') THEN
        ALTER TABLE "RechargeHistory" ADD COLUMN "operatorId" TEXT;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'RechargeHistory' AND column_name = 'phoneNumber') THEN
        ALTER TABLE "RechargeHistory" ADD COLUMN "phoneNumber" TEXT;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'RechargeHistory' AND column_name = 'orderId') THEN
        ALTER TABLE "RechargeHistory" ADD COLUMN "orderId" TEXT;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'RechargeHistory' AND column_name = 'transactionId') THEN
        ALTER TABLE "RechargeHistory" ADD COLUMN "transactionId" TEXT;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'RechargeHistory' AND column_name = 'completedAt') THEN
        ALTER TABLE "RechargeHistory" ADD COLUMN "completedAt" TIMESTAMP(3);
    END IF;
END $$;

-- AlterTable (conditional status column and type change)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'RechargeHistory' AND column_name = 'status') THEN
        ALTER TABLE "RechargeHistory" ADD COLUMN "status" "RechargeStatus" NOT NULL DEFAULT 'PENDING';
    END IF;
    -- Make method column nullable if it's not already
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'RechargeHistory' AND column_name = 'method' AND is_nullable = 'NO') THEN
        ALTER TABLE "RechargeHistory" ALTER COLUMN "method" DROP NOT NULL;
    END IF;
END $$;

-- Note: Most table modifications are skipped as they likely already exist
-- This migration focuses only on essential missing components

-- CreateIndex (with conditional checks)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'Refund_orderId_idx') THEN
        CREATE INDEX "Refund_orderId_idx" ON "Refund"("orderId");
    END IF;
EXCEPTION
    WHEN others THEN null;
END $$;

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'Refund_paymentId_idx') THEN
        CREATE INDEX "Refund_paymentId_idx" ON "Refund"("paymentId");
    END IF;
EXCEPTION
    WHEN others THEN null;
END $$;

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'Refund_status_idx') THEN
        CREATE INDEX "Refund_status_idx" ON "Refund"("status");
    END IF;
EXCEPTION
    WHEN others THEN null;
END $$;

-- AddForeignKey (with conditional checks)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'Refund_orderId_fkey') THEN
        ALTER TABLE "Refund" ADD CONSTRAINT "Refund_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;
EXCEPTION
    WHEN others THEN null;
END $$;

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'Refund_paymentId_fkey') THEN
        ALTER TABLE "Refund" ADD CONSTRAINT "Refund_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;
EXCEPTION
    WHEN others THEN null;
END $$;
