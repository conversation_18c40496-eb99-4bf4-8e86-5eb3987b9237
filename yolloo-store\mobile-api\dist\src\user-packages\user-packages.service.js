"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserPackagesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPackagesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const utils_1 = require("../common/utils");
let UserPackagesService = UserPackagesService_1 = class UserPackagesService {
    prisma;
    logger = new common_1.Logger(UserPackagesService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getUserPackages(userId, query, ctx) {
        console.log('Context in getUserPackages:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            const whereConditions = {
                userId: userId,
                status: {
                    in: ['COMPLETED', 'PROCESSING', 'SHIPPED', 'DELIVERED'],
                },
            };
            const total = await this.prisma.order.count({
                where: whereConditions,
            });
            if (total === 0) {
                return {
                    packages: [],
                    pagination: {
                        total: 0,
                        page: query.page || 1,
                        pageSize: query.pageSize || 10,
                        hasMore: false,
                    },
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            const skip = (query.page - 1) * query.pageSize;
            const orders = await this.prisma.order.findMany({
                where: whereConditions,
                include: {
                    items: true,
                },
                skip,
                take: query.pageSize,
                orderBy: {
                    createdAt: 'desc',
                },
            });
            const productCodes = orders.flatMap(order => order.items.map(item => item.productCode).filter(Boolean));
            const products = await this.prisma.product.findMany({
                where: {
                    sku: { in: productCodes },
                },
                select: {
                    id: true,
                    sku: true,
                    name: true,
                    description: true,
                    images: true,
                    dataSize: true,
                    planType: true,
                    specifications: true,
                },
            });
            const productMap = new Map(products.map(p => [p.sku, p]));
            const packages = orders.flatMap(order => order.items.map(item => this.formatOrderItemAsPackage(order, item, productMap, ctx, isZh)));
            let filteredPackages = packages;
            if (query.status && query.status !== 'all') {
                filteredPackages = packages.filter(pkg => pkg.status === query.status);
            }
            if (query.packageType) {
                filteredPackages = filteredPackages.filter(pkg => pkg.packageType === query.packageType);
            }
            const statusCounts = {
                activating: packages.filter(p => p.status === 'activating').length,
                to_be_activated: packages.filter(p => p.status === 'to_be_activated').length,
                expired: packages.filter(p => p.status === 'expired').length,
                all: packages.length,
            };
            return {
                packages: filteredPackages,
                pagination: {
                    total: filteredPackages.length,
                    page: query.page,
                    pageSize: query.pageSize,
                    hasMore: skip + filteredPackages.length < total,
                },
                statusCounts,
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching user packages:', error);
            return {
                packages: [],
                pagination: {
                    total: 0,
                    page: query.page || 1,
                    pageSize: query.pageSize || 10,
                    hasMore: false,
                },
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
    }
    async getPackageById(userId, packageId, ctx) {
        console.log('Context in getPackageById:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            const orderItem = await this.prisma.orderItem.findFirst({
                where: {
                    id: packageId,
                    order: {
                        userId: userId,
                    },
                },
                include: {
                    order: true,
                },
            });
            if (!orderItem) {
                return {
                    error: 'Package not found',
                    packageId: packageId,
                };
            }
            let product = null;
            if (orderItem.productCode) {
                product = await this.prisma.product.findFirst({
                    where: { sku: orderItem.productCode },
                    select: {
                        id: true,
                        sku: true,
                        name: true,
                        description: true,
                        images: true,
                        dataSize: true,
                        planType: true,
                        specifications: true,
                    },
                });
            }
            const packageDetails = this.formatOrderItemAsDetailedPackage(orderItem, product, ctx, isZh);
            return packageDetails;
        }
        catch (error) {
            this.logger.error('Error fetching package details:', error);
            return {
                error: 'Failed to fetch package details',
                packageId: packageId,
            };
        }
    }
    async activatePackage(userId, activateData, ctx) {
        console.log('Context in activatePackage:', ctx);
        const isZh = ctx.language.startsWith('zh');
        const activation = {
            id: `activation_${Date.now()}`,
            packageId: activateData.packageId,
            userId: userId,
            status: 'processing',
            statusText: isZh ? '激活处理中' : 'Activation in progress',
            activatedAt: new Date().toISOString(),
            estimatedCompletionTime: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
        };
        return {
            activation,
            message: isZh ? '套餐激活请求已提交，预计5分钟内完成激活' : 'Package activation request submitted, expected to complete within 5 minutes',
        };
    }
    async getPackageUsageStats(userId, packageId, query, ctx) {
        console.log('Context in getPackageUsageStats:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            this.logger.log(`Fetching package usage stats for user: ${userId}, package: ${packageId}`);
            const packageUsage = await this.prisma.packageUsage.findFirst({
                where: {
                    userId: userId,
                    packageId: packageId,
                },
                include: {
                    user: true,
                },
            });
            if (!packageUsage) {
                const orderItem = await this.prisma.orderItem.findFirst({
                    where: {
                        id: packageId,
                        order: {
                            userId: userId,
                        },
                    },
                    include: {
                        order: true,
                    },
                });
                if (!orderItem) {
                    return {
                        error: 'Package not found',
                        packageId: packageId,
                    };
                }
                const product = orderItem.productCode ? await this.prisma.product.findFirst({
                    where: { sku: orderItem.productCode },
                }) : null;
                const totalDataMB = product?.dataSize || 10240;
                const usedDataMB = 0;
                const newPackageUsage = await this.prisma.packageUsage.create({
                    data: {
                        userId: userId,
                        orderId: orderItem.orderId,
                        packageId: packageId,
                        packageName: orderItem.variantText || 'Unknown Package',
                        totalData: totalDataMB,
                        usedData: usedDataMB,
                        remainingData: totalDataMB - usedDataMB,
                        validUntil: this.calculateValidUntil(orderItem.order.createdAt, product?.planType || undefined),
                        status: 'active',
                        lastUsedAt: new Date(),
                    },
                });
                return this.formatUsageStats(newPackageUsage, ctx, isZh);
            }
            const remainingData = packageUsage.totalData - packageUsage.usedData;
            if (remainingData !== packageUsage.remainingData) {
                await this.prisma.packageUsage.update({
                    where: { id: packageUsage.id },
                    data: { remainingData: remainingData },
                });
                packageUsage.remainingData = remainingData;
            }
            return this.formatUsageStats(packageUsage, ctx, isZh);
        }
        catch (error) {
            this.logger.error(`Error fetching package usage stats for ${packageId}:`, error);
            return {
                error: 'Failed to fetch usage stats',
                packageId: packageId,
            };
        }
    }
    formatUsageStats(packageUsage, ctx, isZh) {
        const usagePercentage = Math.round((packageUsage.usedData / packageUsage.totalData) * 100);
        const remainingPercentage = 100 - usagePercentage;
        return {
            packageInfo: {
                id: packageUsage.packageId,
                name: packageUsage.packageName,
                description: isZh ? `${this.formatDataSize(packageUsage.totalData)}流量包` : `${this.formatDataSize(packageUsage.totalData)} Data Package`,
                validUntil: packageUsage.validUntil.toISOString().split('T')[0],
                status: packageUsage.status,
                statusText: isZh ? (packageUsage.status === 'active' ? '使用中' : '已过期') : (packageUsage.status === 'active' ? 'Active' : 'Expired'),
                canUpgrade: packageUsage.status === 'active' && remainingPercentage < 20,
                upgradeText: isZh ? '升级套餐' : 'Upgrade Package',
            },
            usageOverview: {
                totalData: this.formatDataSize(packageUsage.totalData),
                usedData: this.formatDataSize(packageUsage.usedData),
                remainingData: this.formatDataSize(packageUsage.remainingData),
                usagePercentage: usagePercentage,
                remainingPercentage: remainingPercentage,
                usedText: isZh ? '已使用流量' : 'Used Data',
                remainingText: isZh ? '剩余流量' : 'Remaining Data',
                progressColor: usagePercentage > 80 ? '#ef4444' : usagePercentage > 60 ? '#f59e0b' : '#10b981',
            },
            actions: [
                {
                    id: 'add_data',
                    text: isZh ? '增加流量' : 'Add Data',
                    icon: 'plus',
                    type: 'primary',
                    action: 'add_data',
                },
                {
                    id: 'data_alert',
                    text: isZh ? '流量提醒' : 'Data Alert',
                    icon: 'bell',
                    type: 'secondary',
                    action: 'set_alert',
                },
            ],
            context: {
                language: ctx.language,
                theme: ctx.theme,
                currency: ctx.currency,
            },
        };
    }
    async getPackageUsageHistory(userId, packageId, query, ctx) {
        console.log('Context in getPackageUsageHistory:', ctx);
        const isZh = ctx.language.startsWith('zh');
        const generateDailyUsage = () => {
            const baseDate = new Date('2025-01-23');
            const dailyUsage = [];
            const usageData = [
                { day: 23, usage: 20 },
                { day: 24, usage: 100 },
                { day: 25, usage: 80 },
                { day: 26, usage: 100 },
                { day: 27, usage: 0 },
                { day: 28, usage: 0 },
                { day: 29, usage: 0 },
            ];
            usageData.forEach(data => {
                const date = new Date(baseDate);
                date.setDate(data.day);
                dailyUsage.push({
                    date: utils_1.DateFormatter.short(date),
                    day: data.day,
                    usage: data.usage,
                    usageText: `${data.usage}MB`,
                    isToday: data.day === 26,
                    formattedDate: isZh ? `${data.day}日` : `${data.day}`,
                });
            });
            return dailyUsage;
        };
        const usageHistory = {
            period: query.period || 'daily',
            chartTitle: isZh ? '2025/1/26' : '2025/1/26',
            chartData: generateDailyUsage(),
            summary: {
                totalUsage: '300MB',
                averageDaily: '43MB',
                peakDay: isZh ? '1月24日' : 'Jan 24',
                peakUsage: '100MB',
            },
            chartConfig: {
                xAxisLabel: isZh ? '日期' : 'Date',
                yAxisLabel: isZh ? '使用量(MB)' : 'Usage (MB)',
                barColor: '#ef4444',
                highlightColor: '#dc2626',
                gridColor: '#f3f4f6',
            },
            pagination: {
                total: 7,
                page: query.page || 1,
                pageSize: query.pageSize || 10,
                hasMore: false,
            },
            context: {
                language: ctx.language,
                theme: ctx.theme,
                currency: ctx.currency,
            },
        };
        return usageHistory;
    }
    formatOrderItemAsPackage(order, item, productMap, ctx, isZh) {
        const product = item.productCode ? productMap.get(item.productCode) : null;
        const status = this.determinePackageStatus(order);
        const statusText = this.getStatusText(status, isZh);
        let packageType = 'domestic';
        let features = [];
        let dataSize = '10GB';
        let productName = item.variantText || 'Unknown Package';
        let productDescription = '';
        if (product) {
            try {
                const specs = typeof product.specifications === 'string'
                    ? JSON.parse(product.specifications)
                    : product.specifications;
                packageType = specs?.packageType || this.determinePackageType(product.name, product.description);
                features = specs?.features || [];
                dataSize = product.dataSize ? this.formatDataSize(product.dataSize) : '10GB';
                productName = product.name;
                productDescription = product.description;
            }
            catch (error) {
                this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
            }
        }
        if (features.length === 0) {
            features = [
                isZh ? '高速网络' : 'High-speed network',
                isZh ? '全天候支持' : '24/7 support',
                isZh ? '即时激活' : 'Instant activation'
            ];
        }
        const usagePercentage = this.generateUsagePercentage(status);
        const usedData = this.calculateUsedData(dataSize, usagePercentage);
        const validUntil = this.calculateValidUntil(order.createdAt, product?.planType);
        const activatedAt = order.createdAt;
        return {
            id: item.id,
            name: productName,
            description: productDescription,
            packageType: packageType,
            status: status,
            statusText: statusText,
            dataSize: dataSize,
            usedData: usedData,
            usagePercentage: usagePercentage,
            validUntil: validUntil,
            activatedAt: activatedAt.toISOString(),
            price: Number(item.price),
            currency: ctx.currency,
            features: features,
            imageUrl: product?.images && product.images.length > 0
                ? product.images[0]
                : 'https://example.com/default-package.jpg',
            progressColor: this.getProgressColor(status),
        };
    }
    formatOrderItemAsDetailedPackage(orderItem, product, ctx, isZh) {
        const productMap = new Map();
        if (product && orderItem.productCode) {
            productMap.set(orderItem.productCode, product);
        }
        const basicPackage = this.formatOrderItemAsPackage(orderItem.order, orderItem, productMap, ctx, isZh);
        const remainingData = this.calculateRemainingData(basicPackage.dataSize, basicPackage.usagePercentage);
        return {
            ...basicPackage,
            remainingData: remainingData,
            detailedInfo: {
                activation: isZh ? '购买后立即激活' : 'Instant activation after purchase',
                coverage: isZh ? '全国覆盖' : 'Nationwide coverage',
                speed: isZh ? '5G网络下载速度最高1Gbps' : '5G network download speed up to 1Gbps',
                usage: isZh ? '适合日常使用、商务出行' : 'Perfect for daily use and business travel',
            },
            usageHistory: this.generateUsageHistory(basicPackage.dataSize, basicPackage.usagePercentage, isZh),
            restrictions: [
                isZh ? '仅限购买用户使用' : 'Valid only for purchasing user',
                isZh ? '不可转让或退款' : 'Non-transferable and non-refundable',
                isZh ? '按套餐条款使用' : 'Subject to package terms',
            ],
        };
    }
    determinePackageStatus(order) {
        const now = new Date();
        const orderDate = new Date(order.createdAt);
        const daysSinceOrder = Math.floor((now.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24));
        if (order.status === 'PENDING' || order.status === 'PROCESSING') {
            return 'to_be_activated';
        }
        else if (order.status === 'COMPLETED' || order.status === 'DELIVERED') {
            if (daysSinceOrder < 1) {
                return 'activating';
            }
            else if (daysSinceOrder < 30) {
                return 'activating';
            }
            else {
                return 'expired';
            }
        }
        return 'to_be_activated';
    }
    getStatusText(status, isZh) {
        const statusMap = {
            activating: { zh: '激活中', en: 'Activating' },
            to_be_activated: { zh: '待激活', en: 'To be activated' },
            expired: { zh: '已过期', en: 'Expired' },
        };
        return statusMap[status] ? (isZh ? statusMap[status].zh : statusMap[status].en) : status;
    }
    getProgressColor(status) {
        const colorMap = {
            activating: '#ef4444',
            to_be_activated: '#f59e0b',
            expired: '#6b7280',
        };
        return colorMap[status] || '#ef4444';
    }
    determinePackageType(name, description) {
        const text = (name + ' ' + description).toLowerCase();
        if (text.includes('漫游') || text.includes('roaming') || text.includes('international'))
            return 'roaming';
        if (text.includes('本地') || text.includes('local') || text.includes('city'))
            return 'local';
        return 'domestic';
    }
    generateUsagePercentage(status) {
        if (status === 'expired')
            return Math.floor(Math.random() * 20) + 80;
        if (status === 'activating')
            return Math.floor(Math.random() * 60) + 20;
        return Math.floor(Math.random() * 30);
    }
    calculateUsedData(totalData, usagePercentage) {
        const totalMB = this.parseDataSize(totalData);
        const usedMB = Math.floor(totalMB * usagePercentage / 100);
        return this.formatDataSize(usedMB);
    }
    calculateRemainingData(totalData, usagePercentage) {
        const totalMB = this.parseDataSize(totalData);
        const remainingMB = Math.floor(totalMB * (100 - usagePercentage) / 100);
        return this.formatDataSize(remainingMB);
    }
    calculateValidUntil(createdAt, planType) {
        const created = new Date(createdAt);
        let validUntil;
        if (planType === 'Daily') {
            validUntil = utils_1.DateUtils.addDays(created, 1);
        }
        else if (planType === 'Weekly') {
            validUntil = utils_1.DateUtils.addDays(created, 7);
        }
        else {
            validUntil = utils_1.DateUtils.addDays(created, 30);
        }
        return validUntil.toISOString().split('T')[0];
    }
    generateUsageHistory(totalData, usagePercentage, isZh) {
        const history = [];
        const days = Math.min(7, Math.floor(usagePercentage / 10));
        for (let i = 0; i < days; i++) {
            const date = utils_1.DateUtils.addDays(new Date(), -i);
            const dailyUsage = Math.floor(Math.random() * 500) + 100;
            history.push({
                date: date.toISOString().split('T')[0],
                usage: this.formatDataSize(dailyUsage),
                description: isZh ? '日常使用' : 'Daily usage'
            });
        }
        return history.reverse();
    }
    parseDataSize(dataSize) {
        const match = dataSize.match(/(\d+(?:\.\d+)?)\s*(MB|GB)/i);
        if (!match)
            return 1024;
        const value = parseFloat(match[1]);
        const unit = match[2].toUpperCase();
        return unit === 'GB' ? value * 1024 : value;
    }
    formatDataSize(dataSize) {
        if (dataSize >= 1024) {
            const sizeInGB = dataSize / 1024;
            return sizeInGB % 1 === 0 ? `${sizeInGB}GB` : `${sizeInGB.toFixed(1)}GB`;
        }
        else {
            return `${Math.round(dataSize)}MB`;
        }
    }
};
UserPackagesService = UserPackagesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UserPackagesService);
exports.UserPackagesService = UserPackagesService;
//# sourceMappingURL=user-packages.service.js.map