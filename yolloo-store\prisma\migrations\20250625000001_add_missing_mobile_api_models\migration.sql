-- CreateEnum
CREATE TYPE "RechargeStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED');

-- CreateEnum  
CREATE TYPE "RefundStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "UploadCategory" AS ENUM ('products', 'categories', 'users', 'rewards', 'temp');

-- CreateTable
CREATE TABLE "Refund" (
    "id" TEXT NOT NULL,
    "orderId" TEXT,
    "paymentId" TEXT,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "reason" TEXT,
    "status" "RefundStatus" NOT NULL DEFAULT 'PENDING',
    "processedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Refund_pkey" PRIMARY KEY ("id")
);

-- AlterTable
ALTER TABLE "RechargeHistory" ADD COLUMN     "operatorId" TEXT,
ADD COLUMN     "phoneNumber" TEXT,
ADD COLUMN     "status" "RechargeStatus" NOT NULL DEFAULT 'PENDING',
ADD COLUMN     "orderId" TEXT,
ADD COLUMN     "transactionId" TEXT,
ADD COLUMN     "completedAt" TIMESTAMP(3);

-- AlterTable  
ALTER TABLE "RechargeHistory" ALTER COLUMN "method" DROP NOT NULL,
ALTER COLUMN "status" DROP DEFAULT;

-- AlterTable
ALTER TABLE "Upload" ADD COLUMN     "fileName" TEXT,
ADD COLUMN     "path" TEXT,
ADD COLUMN     "category" "UploadCategory",
ADD COLUMN     "uploadedBy" TEXT,
ADD COLUMN     "uploadedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "Upload" ALTER COLUMN "filename" DROP NOT NULL,
ALTER COLUMN "originalName" DROP NOT NULL,
ALTER COLUMN "mimeType" DROP NOT NULL,
ALTER COLUMN "url" DROP NOT NULL,
ALTER COLUMN "userId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "PaymentCard" ADD COLUMN     "type" TEXT,
ADD COLUMN     "brand" TEXT,
ADD COLUMN     "last4" TEXT;

-- AlterTable
ALTER TABLE "PaymentCard" ALTER COLUMN "cardNumber" DROP NOT NULL,
ALTER COLUMN "cardHolder" DROP NOT NULL,
ALTER COLUMN "cardType" DROP NOT NULL,
ALTER COLUMN "isActive" DROP NOT NULL;

-- AlterTable
ALTER TABLE "MobileOperator" ADD COLUMN     "code" TEXT,
ADD COLUMN     "nameEn" TEXT,
ADD COLUMN     "nameZh" TEXT,
ADD COLUMN     "logoUrl" TEXT;

-- AlterTable
ALTER TABLE "MobileOperator" ALTER COLUMN "name" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Page" ADD COLUMN     "pageId" TEXT,
ADD COLUMN     "styles" TEXT,
ADD COLUMN     "language" TEXT NOT NULL DEFAULT 'en';

-- AlterTable
ALTER TABLE "Page" ALTER COLUMN "slug" DROP NOT NULL,
ALTER COLUMN "content" SET DATA TYPE TEXT,
ALTER COLUMN "metaTitle" DROP NOT NULL,
ALTER COLUMN "metaDesc" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SocialAccount" ADD COLUMN     "accessToken" TEXT,
ADD COLUMN     "refreshToken" TEXT,
ADD COLUMN     "expiresAt" TIMESTAMP(3),
ADD COLUMN     "tokenType" TEXT,
ADD COLUMN     "scope" TEXT,
ADD COLUMN     "idToken" TEXT,
ADD COLUMN     "sessionState" TEXT,
ADD COLUMN     "picture" TEXT,
ADD COLUMN     "lastLoginAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "SocialAccount" ALTER COLUMN "providerId" SET DATA TYPE TEXT,
ALTER COLUMN "providerId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "ProductView" ALTER COLUMN "userId" DROP NOT NULL;

-- CreateIndex
CREATE INDEX "Refund_orderId_idx" ON "Refund"("orderId");

-- CreateIndex  
CREATE INDEX "Refund_paymentId_idx" ON "Refund"("paymentId");

-- CreateIndex
CREATE INDEX "Refund_status_idx" ON "Refund"("status");

-- CreateIndex
CREATE INDEX "RechargeHistory_operatorId_idx" ON "RechargeHistory"("operatorId");

-- CreateIndex
CREATE INDEX "RechargeHistory_userId_status_idx" ON "RechargeHistory"("userId", "status");

-- CreateIndex
CREATE UNIQUE INDEX "MobileOperator_code_key" ON "MobileOperator"("code");

-- CreateIndex
CREATE UNIQUE INDEX "Page_pageId_key" ON "Page"("pageId");

-- CreateIndex
CREATE INDEX "Page_pageId_language_idx" ON "Page"("pageId", "language");

-- CreateIndex
CREATE INDEX "Upload_category_idx" ON "Upload"("category");

-- CreateIndex
CREATE INDEX "Upload_uploadedAt_idx" ON "Upload"("uploadedAt");

-- CreateIndex
CREATE INDEX "Upload_uploadedBy_idx" ON "Upload"("uploadedBy");

-- CreateIndex
CREATE UNIQUE INDEX "SocialAccount_provider_providerAccountId_key" ON "SocialAccount"("provider", "providerAccountId");

-- AddForeignKey
ALTER TABLE "Refund" ADD CONSTRAINT "Refund_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Refund" ADD CONSTRAINT "Refund_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RechargeHistory" ADD CONSTRAINT "RechargeHistory_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "MobileOperator"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Upload" ADD CONSTRAINT "Upload_uploadedBy_fkey" FOREIGN KEY ("uploadedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
