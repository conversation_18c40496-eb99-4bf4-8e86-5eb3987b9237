/// <reference types="multer" />
import { PrismaService } from '../prisma.service';
export declare class UploadService {
    private prisma;
    private readonly logger;
    private readonly uploadDir;
    private readonly allowedMimeTypes;
    private readonly maxFileSize;
    constructor(prisma: PrismaService);
    private ensureUploadDirectory;
    uploadImage(file: Express.Multer.File, category?: 'products' | 'users' | 'categories' | 'rewards' | 'temp', userId?: string): Promise<{
        id: string;
        fileName: string;
        originalName: string;
        url: string;
        size: number;
        mimeType: string;
        category: "products" | "categories" | "users" | "rewards" | "temp";
        uploadedAt: Date;
    }>;
    uploadMultipleImages(files: Express.Multer.File[], category?: 'products' | 'users' | 'categories' | 'rewards' | 'temp', userId?: string): Promise<{
        success: any[];
        errors: any[];
        total: number;
        uploaded: number;
        failed: number;
    }>;
    getUploadById(uploadId: string): Promise<import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        filename: string;
        originalName: string;
        mimeType: string;
        size: number;
        url: string;
        path: string | null;
        category: string | null;
        uploadedBy: string | null;
        createdAt: Date;
    }, unknown> & {}>;
    deleteUpload(uploadId: string, userId?: string): Promise<{
        success: boolean;
        message: string;
    }>;
    getUserUploads(userId: string, category?: string): Promise<(import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        filename: string;
        originalName: string;
        mimeType: string;
        size: number;
        url: string;
        path: string | null;
        category: string | null;
        uploadedBy: string | null;
        createdAt: Date;
    }, unknown> & {})[]>;
    cleanupTempFiles(olderThanHours?: number): Promise<{
        deletedCount: number;
    }>;
    private validateFile;
    getImageUrl(uploadId: string): string;
    moveUploadToCategory(uploadId: string, newCategory: 'products' | 'users' | 'categories' | 'rewards' | 'temp'): Promise<import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        filename: string;
        originalName: string;
        mimeType: string;
        size: number;
        url: string;
        path: string | null;
        category: string | null;
        uploadedBy: string | null;
        createdAt: Date;
    }, unknown> & {}>;
}
